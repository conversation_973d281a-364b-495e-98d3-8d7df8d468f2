const app = getApp()

Page({
  data: {
    orderId: '',
    order: {},
    openId: '',
    statusIcon: '/images/icons/pending.png', // 默认状态图标
    statusText: {
      'pending': '待接单',
      'accepted': '待上门',
      'processing': '维修中',
      'completed': '已完成'
    },
    faultTypeMap: {
      'no_charging': '无法充电',
      'slow_charging': '充电慢',
      'error_code': '报错代码',
      'port_damage': '接口损坏',
      'not_starting': '无法启动',
      'overheating': '过热',
      'display_issue': '显示故障',
      'other': '其他故障'
    }
  },

  onLoad: function (options) {

    // 获取订单ID
    const orderId = options.id;
    if (!orderId) {
      tt.showToast({
        title: '订单ID不能为空',
        icon: 'none'
      });
      return;
    }

    this.setData({
      orderId: orderId
    });

    // 获取openId
    this.getOpenId();
  },

  onShow: function() {
    // 如果已经有openId和orderId，则获取订单详情
    if (this.data.openId && this.data.orderId) {
      this.getOrderDetail();
    }
  },

  // 获取openId
  getOpenId: function() {
    // 从全局数据中获取openId
    if (app.globalData.openId) {
      this.setData({
        openId: app.globalData.openId
      });
      this.getOrderDetail();
    } else {
      // 从本地存储中获取
      tt.getStorage({
        key: 'userInfo',
        success: (res) => {
          if (res.data && res.data.openId) {
            this.setData({
              openId: res.data.openId
            });
            this.getOrderDetail();
          } else {
            // 尝试从openId存储中获取
            tt.getStorage({
              key: 'openId',
              success: (res) => {
                if (res.data) {
                  this.setData({
                    openId: res.data
                  });
                  this.getOrderDetail();
                } else {
                  this.showLoginTip();
                }
              },
              fail: () => {
                this.showLoginTip();
              }
            });
          }
        },
        fail: () => {
          // 尝试从openId存储中获取
          tt.getStorage({
            key: 'openId',
            success: (res) => {
              if (res.data) {
                this.setData({
                  openId: res.data
                });
                this.getOrderDetail();
              } else {
                this.showLoginTip();
              }
            },
            fail: () => {
              this.showLoginTip();
            }
          });
        }
      });
    }
  },

  // 显示登录提示
  showLoginTip: function() {
    // 如果没有openId，提示用户登录
    tt.showToast({
      title: '请先登录',
      icon: 'none'
    });

    // 跳转到用户中心页面
    tt.switchTab({
      url: '/pages/user_center/user_center'
    });
  },

  // 获取订单详情
  getOrderDetail: function() {
    if (!this.data.openId || !this.data.orderId) {
      return;
    }

    tt.showLoading({
      title: '加载中...'
    });

    const api = require('../../utils/api');
    api.getRepairOrderDetail(this.data.orderId, this.data.openId).then(res => {
      tt.hideLoading();

      if (res.success && res.order) {
        // 格式化创建时间
        const order = res.order;

        // 调试：打印工程师头像信息

        if (order.createdAt) {
          const createdDate = new Date(order.createdAt);
          order.createdAtFormatted = `${createdDate.getFullYear()}-${this.padZero(createdDate.getMonth() + 1)}-${this.padZero(createdDate.getDate())} ${this.padZero(createdDate.getHours())}:${this.padZero(createdDate.getMinutes())}`;
        } else {
          order.createdAtFormatted = '未知';
        }

        // 格式化更新时间
        if (order.updatedAt) {
          const updatedDate = new Date(order.updatedAt);
          order.updatedAtFormatted = `${updatedDate.getFullYear()}-${this.padZero(updatedDate.getMonth() + 1)}-${this.padZero(updatedDate.getDate())} ${this.padZero(updatedDate.getHours())}:${this.padZero(updatedDate.getMinutes())}`;
        }

        // 解析图片JSON字符串
        if (order.images && typeof order.images === 'string') {
          try {
            order.images = JSON.parse(order.images);
          } catch (e) {
            order.images = [];
          }
        } else if (!order.images) {
          order.images = [];
        }

        // 处理耗材明细JSON字符串
        if (order.materialsDetail && typeof order.materialsDetail === 'string') {
          try {
            order.materialsDetail = JSON.parse(order.materialsDetail);
          } catch (e) {
            order.materialsDetail = [];
          }
        } else if (!order.materialsDetail) {
          order.materialsDetail = [];
        }

        // 格式化完成时间
        if (order.completedAt) {
          const completedDate = new Date(order.completedAt);
          order.completedAtFormatted = `${completedDate.getFullYear()}-${this.padZero(completedDate.getMonth() + 1)}-${this.padZero(completedDate.getDate())} ${this.padZero(completedDate.getHours())}:${this.padZero(completedDate.getMinutes())}`;
        }

        // 计算总费用
        if (order.repairFee || order.partsFee) {
          const repairFee = parseFloat(order.repairFee) || 0;
          const partsFee = parseFloat(order.partsFee) || 0;
          order.totalFee = (repairFee + partsFee).toFixed(2);
        }

        // 处理工程师头像
        if (order.engineerAvatar) {
          order.processedEngineerAvatar = this.processAvatarUrl(order.engineerAvatar);
        } else {
          order.processedEngineerAvatar = '';
        }

        // 根据订单状态设置状态图标
        let statusIcon = '/images/icons/pending.png'; // 默认图标
        switch (order.status) {
          case 'pending':
            statusIcon = '/images/icons/pending.png';
            break;
          case 'accepted':
            statusIcon = '/images/icons/accepted.png';
            break;
          case 'processing':
            statusIcon = '/images/icons/processing.png';
            break;
          case 'completed':
            statusIcon = '/images/icons/completed.png';
            break;
        }

        this.setData({
          order: order,
          statusIcon: statusIcon
        });
      } else {
        tt.showToast({
          title: '获取订单详情失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      tt.hideLoading();
      tt.showToast({
        title: '获取订单详情失败',
        icon: 'none'
      });
    });
  },

  // 预览图片
  previewImage: function(e) {
    const url = e.currentTarget.dataset.url;
    const images = this.data.order.images;

    tt.previewImage({
      current: url,
      urls: images
    });
  },

  // 拨打工程师电话
  callEngineer: function(e) {
    const phone = e.currentTarget.dataset.phone;

    if (!phone) {
      tt.showToast({
        title: '电话号码不能为空',
        icon: 'none'
      });
      return;
    }

    tt.makePhoneCall({
      phoneNumber: phone
    });
  },

  // 联系客服
  contactService: function() {
    // 跳转到客服页面
    tt.navigateTo({
      url: '/pages/customer_service/customer_service'
    });
  },

  // 取消订单
  cancelOrder: function() {
    tt.showModal({
      title: '提示',
      content: '确定要取消该维修订单吗？',
      success: (res) => {
        if (res.confirm) {
          this.doCancelOrder();
        }
      }
    });
  },

  // 执行取消订单
  doCancelOrder: function() {
    if (!this.data.openId || !this.data.orderId) {
      return;
    }

    tt.showLoading({
      title: '取消中...'
    });

    const api = require('../../utils/api');
    api.cancelRepairOrder(this.data.orderId, this.data.openId).then(res => {
      tt.hideLoading();

      if (res.success) {
        tt.showToast({
          title: '取消成功',
          icon: 'success'
        });

        // 刷新订单详情
        this.getOrderDetail();
      } else {
        tt.showToast({
          title: res.message || '取消失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      tt.hideLoading();
      tt.showToast({
        title: '取消失败',
        icon: 'none'
      });
    });
  },

  // 去预约维修
  goToRepair: function() {
    tt.navigateTo({
      url: '/pages/repair_form/repair_form'
    });
  },

  // 数字补零
  padZero: function(num) {
    return num < 10 ? '0' + num : num;
  },

  // 处理头像URL
  processAvatarUrl: function(avatarUrl) {
    if (!avatarUrl || avatarUrl.trim() === '') {
      return '';
    }

    // 如果是完整的HTTP URL，直接返回
    if (avatarUrl.startsWith('http')) {
      return avatarUrl;
    }

    // 如果已经包含域名，直接返回
    if (avatarUrl.startsWith('https://www.zhuanglz.cn:8443') || avatarUrl.startsWith('https://your-domain.com')) {
      return avatarUrl;
    }

    // 如果是相对路径，添加服务器地址
    if (avatarUrl.startsWith('/uploads/')) {
      return `https://www.zhuanglz.cn:8443${avatarUrl}`;
    }

    // 如果只是文件名，添加完整路径
    return `https://www.zhuanglz.cn:8443/uploads/${avatarUrl}`;
  },

  // 获取维修结果文本
  getRepairResultText: function(result) {
    const resultMap = {
      'success': '维修成功',
      'partial': '部分修复',
      'failed': '维修失败',
      'replacement': '需要更换设备'
    };
    return resultMap[result] || '未知';
  },

  // 拨打工程师电话
  callEngineer: function(e) {
    const phone = e.currentTarget.dataset.phone;
    if (phone) {
      tt.makePhoneCall({
        phoneNumber: phone,
        success: function() {
        },
        fail: function(err) {
          tt.showToast({
            title: '拨打电话失败',
            icon: 'none'
          });
        }
      });
    } else {
      tt.showToast({
        title: '电话号码不存在',
        icon: 'none'
      });
    }
  },

  // 头像加载失败处理
  onEngineerAvatarError: function(e) {

    // 将头像设置为空，显示默认占位符
    const order = this.data.order;
    order.processedEngineerAvatar = '';

    this.setData({
      order: order
    });
  }
})
