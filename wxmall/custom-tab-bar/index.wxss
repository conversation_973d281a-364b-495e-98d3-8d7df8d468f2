.tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background: white;
  display: flex;
  padding-bottom: env(safe-area-inset-bottom);
  z-index: 999;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.tab-bar-border {
  background-color: #f0f0f0;
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 1px;
  transform: scaleY(0.5);
}

.tab-bar-item {
  flex: 1;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  position: relative;
  padding: 10rpx 0;
}

.tab-bar-item-text {
  font-size: 26rpx;
  color: #757575;
  line-height: 1.2;
  font-weight: 400;
  transition: all 0.3s ease;
}

.tab-bar-item-active .tab-bar-item-text {
  color: #1e88e5;
  font-weight: 500;
  font-size: 28rpx;
}

/* 添加底部指示器 */
.tab-bar-item-active::after {
  content: '';
  position: absolute;
  bottom: 8rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 20rpx;
  height: 3rpx;
  border-radius: 2rpx;
  background-color: #1e88e5;
}
