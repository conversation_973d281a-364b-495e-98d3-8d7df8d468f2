Component({
  data: {
    selected: 0,
    color: "#757575",
    selectedColor: "#1e88e5",
    list: [
      {
        pagePath: "/pages/index/index",
        text: "首页"
      },
      {
        pagePath: "/pages/mall/mall", 
        text: "产品"
      },
      {
        pagePath: "/pages/repair/repair",
        text: "维修"
      },
      {
        pagePath: "/pages/user_center/user_center",
        text: "我的"
      }
    ]
  },
  attached() {
  },
  methods: {
    switchTab(e) {
      const data = e.currentTarget.dataset
      const url = data.path
      wx.switchTab({url})
      this.setData({
        selected: data.index
      })
    }
  }
})
