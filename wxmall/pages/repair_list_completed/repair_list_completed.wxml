<!-- 严格按照抖音小程序repair_list_completed模板设计 -->
<view class="container">
  <view class="header">
    <view class="title">已完成</view>
  </view>

  <view class="content">
    <block wx:if="{{orders.length > 0}}">
      <view class="order-list">
        <view class="order-item" wx:for="{{orders}}" wx:key="id" bindtap="goToOrderDetail" data-id="{{item.orderNo}}">
          <view class="order-header">
            <view class="order-no">订单号: {{item.orderNo}}</view>
            <view class="order-status">
              <text class="status-text status-completed">已完成</text>
            </view>
          </view>
          <view class="engineer-info" wx:if="{{item.engineer}}">
            <view class="engineer-avatar">
              <image src="{{item.engineer.avatar || '/images/default-avatar.png'}}" mode="aspectFill"></image>
            </view>
            <view class="engineer-details">
              <view class="engineer-name">{{item.engineer.name}}</view>
              <view class="engineer-phone">{{item.engineer.phone}}</view>
              <view class="engineer-rating">
                <text class="rating-text">评分: {{item.engineer.rating}}</text>
                <text class="service-count">服务次数: {{item.engineer.serviceCount}}</text>
              </view>
            </view>
            <view class="rating-section" wx:if="{{!item.userRating}}">
              <button class="rate-btn" catchtap="showRatingModal" data-order="{{item.orderNo}}">评价</button>
            </view>
            <view class="rated-section" wx:else>
              <view class="user-rating">
                <text class="rating-label">已评价:</text>
                <text class="rating-stars">{{item.userRating.stars}}</text>
              </view>
            </view>
          </view>
          <view class="completion-info">
            <view class="completion-title">维修结果</view>
            <view class="completion-summary">{{item.completionSummary}}</view>
            <view class="completion-images" wx:if="{{item.completionImages && item.completionImages.length > 0}}">
              <image 
                class="completion-image" 
                wx:for="{{item.completionImages}}" 
                wx:key="*this" 
                src="{{item}}" 
                mode="aspectFill"
                bindtap="previewCompletionImage"
                data-url="{{item}}"
                data-urls="{{item.completionImages}}"
              ></image>
            </view>
          </view>
          <view class="order-info">
            <view class="info-item">
              <text class="label">故障类型:</text>
              <text class="value">{{faultTypeMap[item.faultType] || '未知故障'}}</text>
            </view>
            <view class="info-item">
              <text class="label">充电桩型号:</text>
              <text class="value">{{item.model}}</text>
            </view>
            <view class="info-item">
              <text class="label">维修费用:</text>
              <text class="value price">¥{{item.totalCost}}</text>
            </view>
            <view class="info-item">
              <text class="label">服务地址:</text>
              <text class="value address">{{item.fullAddress}}</text>
            </view>
          </view>
          <view class="order-footer">
            <view class="time">完成时间: {{item.completedAtFormatted}}</view>
            <view class="actions">
              <button class="btn btn-default" catchtap="contactService">联系客服</button>
              <button class="btn btn-primary" catchtap="reorderService" data-order="{{item.orderNo}}">再次预约</button>
            </view>
          </view>
        </view>
      </view>
    </block>
    <view class="empty-state" wx:else>
      <image class="empty-icon" src="/images/icons/empty.png" mode="aspectFit"></image>
      <text class="empty-text">暂无已完成订单</text>
    </view>
  </view>

  <!-- 评价弹窗 -->
  <view class="rating-modal {{showRatingModal ? 'show' : ''}}" wx:if="{{showRatingModal}}">
    <view class="modal-mask" bindtap="hideRatingModal"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">服务评价</text>
        <text class="modal-close" bindtap="hideRatingModal">×</text>
      </view>
      <view class="modal-body">
        <view class="rating-stars">
          <text 
            class="star {{index < currentRating ? 'active' : ''}}" 
            wx:for="{{5}}" 
            wx:key="*this"
            bindtap="selectRating"
            data-rating="{{index + 1}}"
          >★</text>
        </view>
        <textarea 
          class="rating-comment" 
          placeholder="请输入您的评价（选填）"
          value="{{ratingComment}}"
          bindinput="onRatingCommentInput"
        ></textarea>
      </view>
      <view class="modal-footer">
        <button class="submit-rating-btn" bindtap="submitRating">提交评价</button>
      </view>
    </view>
  </view>
</view>
