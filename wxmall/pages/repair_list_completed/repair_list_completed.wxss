/* 严格按照抖音小程序repair_list_completed模板设计 */
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header {
  background-color: #fff;
  padding: 30rpx;
  text-align: center;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
}

.title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.content {
  flex: 1;
  padding: 30rpx;
}

.order-list {
  margin-bottom: 40rpx;
}

.order-item {
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #eee;
}

.order-no {
  font-size: 28rpx;
  color: #666;
}

.order-status {
  font-size: 28rpx;
}

.status-text {
  padding: 6rpx 16rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
}

.status-completed {
  background-color: #f9f9f9;
  color: #8c8c8c;
}

.engineer-info {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 2rpx solid #eee;
}

.engineer-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 20rpx;
}

.engineer-avatar image {
  width: 100%;
  height: 100%;
}

.engineer-details {
  flex: 1;
}

.engineer-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.engineer-phone {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.engineer-rating {
  display: flex;
  gap: 20rpx;
}

.rating-text,
.service-count {
  font-size: 22rpx;
  color: #999;
}

.rating-section {
  display: flex;
  align-items: center;
}

.rate-btn {
  background-color: #ff9800;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 8rpx 16rpx;
  font-size: 24rpx;
}

.rated-section {
  display: flex;
  align-items: center;
}

.user-rating {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.rating-label {
  font-size: 24rpx;
  color: #666;
}

.rating-stars {
  font-size: 24rpx;
  color: #ff9800;
}

.completion-info {
  padding: 20rpx 0;
  border-bottom: 2rpx solid #eee;
}

.completion-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 16rpx;
}

.completion-summary {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 16rpx;
}

.completion-images {
  display: flex;
  gap: 16rpx;
  flex-wrap: wrap;
}

.completion-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
}

.order-info {
  padding: 20rpx 0;
}

.info-item {
  display: flex;
  margin-bottom: 16rpx;
}

.label {
  width: 160rpx;
  color: #999;
  font-size: 26rpx;
}

.value {
  flex: 1;
  color: #333;
  font-size: 26rpx;
}

.price {
  color: #ff4d4f;
  font-weight: 500;
}

.address {
  word-break: break-all;
}

.order-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20rpx;
  border-top: 2rpx solid #eee;
}

.time {
  font-size: 24rpx;
  color: #999;
}

.actions {
  display: flex;
}

.btn {
  margin-left: 20rpx;
  font-size: 24rpx;
  padding: 8rpx 24rpx;
  border-radius: 8rpx;
}

.btn-primary {
  background-color: #1890ff;
  color: #fff;
  border: none;
}

.btn-default {
  background-color: #fff;
  color: #666;
  border: 2rpx solid #d9d9d9;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  color: #999;
  font-size: 28rpx;
  margin-bottom: 40rpx;
}

/* 评价弹窗样式 */
.rating-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  visibility: hidden;
  opacity: 0;
  transition: all 0.3s ease;
}

.rating-modal.show {
  visibility: visible;
  opacity: 1;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  border-top-left-radius: 24rpx;
  border-top-right-radius: 24rpx;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.rating-modal.show .modal-content {
  transform: translateY(0);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.modal-close {
  font-size: 48rpx;
  color: #999;
  line-height: 1;
}

.modal-body {
  padding: 30rpx;
}

.rating-stars {
  display: flex;
  justify-content: center;
  gap: 16rpx;
  margin-bottom: 30rpx;
}

.star {
  font-size: 48rpx;
  color: #d9d9d9;
  transition: color 0.2s;
}

.star.active {
  color: #ff9800;
}

.rating-comment {
  width: 100%;
  min-height: 120rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  padding: 16rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.modal-footer {
  padding: 30rpx;
  border-top: 2rpx solid #f0f0f0;
}

.submit-rating-btn {
  width: 100%;
  height: 88rpx;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 8rpx;
  font-size: 32rpx;
}
