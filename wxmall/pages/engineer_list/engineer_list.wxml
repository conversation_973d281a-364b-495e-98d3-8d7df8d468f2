<view class="container">
  <!-- 搜索栏 -->
  <view class="search-bar">
    <view class="search-input-wrapper">
      <input class="search-input" 
             placeholder="搜索工程师姓名或专业领域" 
             value="{{searchKeyword}}" 
             bindinput="onSearchInput" />
      <text class="search-icon">🔍</text>
    </view>
    <view class="filter-btn" bindtap="toggleFilter">
      <text>筛选</text>
      <text class="filter-icon">{{filterOptions.showFilter ? '▲' : '▼'}}</text>
    </view>
  </view>

  <!-- 专业领域标签 -->
  <scroll-view class="specialty-tabs" scroll-x="{{true}}" wx:if="{{specialties.length > 0}}">
    <view class="specialty-tab-wrapper">
      <view class="specialty-tab {{currentSpecialty === item.value ? 'active' : ''}}" 
            wx:for="{{specialties}}" 
            wx:key="value"
            bindtap="onSpecialtyChange" 
            data-specialty="{{item.value}}">
        {{item.label}}
      </view>
    </view>
  </scroll-view>

  <!-- 排序栏 -->
  <view class="sort-bar">
    <view class="sort-options">
      <view class="sort-option {{sortType === 'rating' ? 'active' : ''}}" 
            bindtap="onSortChange" 
            data-sort="rating">
        评分排序
      </view>
      <view class="sort-option {{sortType === 'experience' ? 'active' : ''}}" 
            bindtap="onSortChange" 
            data-sort="experience">
        经验排序
      </view>
      <view class="sort-option {{sortType === 'orders' ? 'active' : ''}}" 
            bindtap="onSortChange" 
            data-sort="orders">
        订单排序
      </view>
    </view>
    <view class="result-count">共{{filteredEngineers.length}}位工程师</view>
  </view>

  <!-- 筛选面板 -->
  <view class="filter-panel {{filterOptions.showFilter ? 'show' : ''}}">
    <!-- 经验筛选 -->
    <view class="filter-section">
      <view class="filter-title">工作经验</view>
      <view class="filter-options">
        <view class="filter-option {{filterOptions.selectedExperience === item.value ? 'active' : ''}}"
              wx:for="{{filterOptions.experienceRanges}}"
              wx:key="value"
              bindtap="onExperienceChange"
              data-experience="{{item.value}}">
          {{item.label}}
        </view>
      </view>
    </view>

    <!-- 评分筛选 -->
    <view class="filter-section">
      <view class="filter-title">服务评分</view>
      <view class="filter-options">
        <view class="filter-option {{filterOptions.selectedRating === item.value ? 'active' : ''}}"
              wx:for="{{filterOptions.ratingRanges}}"
              wx:key="value"
              bindtap="onRatingChange"
              data-rating="{{item.value}}">
          {{item.label}}
        </view>
      </view>
    </view>

    <!-- 筛选操作 -->
    <view class="filter-actions">
      <view class="filter-reset" bindtap="resetFilter">重置</view>
      <view class="filter-confirm" bindtap="toggleFilter">确定</view>
    </view>
  </view>

  <!-- 工程师列表 -->
  <view class="engineer-list">
    <!-- 加载状态 -->
    <view class="loading" wx:if="{{loading}}">
      <view class="loading-spinner"></view>
      <text>正在加载工程师信息...</text>
    </view>

    <!-- 空状态 -->
    <view class="empty" wx:elif="{{filteredEngineers.length === 0}}">
      <view class="empty-icon">👨‍🔧</view>
      <view class="empty-title">暂无工程师</view>
      <view class="empty-desc">{{searchKeyword ? '未找到相关工程师，请尝试其他关键词' : '当前筛选条件下暂无工程师'}}</view>
      <view class="empty-action" wx:if="{{searchKeyword || currentSpecialty || filterOptions.selectedExperience || filterOptions.selectedRating}}" bindtap="resetFilter">
        <text>清除筛选条件</text>
      </view>
    </view>

    <!-- 工程师卡片 -->
    <view class="engineer-card" 
          wx:for="{{filteredEngineers}}" 
          wx:key="id"
          bindtap="viewEngineerDetail"
          data-id="{{item.id}}">
      
      <!-- 工程师头部信息 -->
      <view class="engineer-header">
        <!-- 头像 - 与repair页面保持一致的显示逻辑 -->
        <view class="engineer-avatar-container">
          <image
            src="{{item.processedAvatar}}"
            class="engineer-avatar"
            mode="aspectFill"
            binderror="onAvatarError"
            data-index="{{index}}"
            wx:if="{{item.processedAvatar}}"
          />
          <view class="avatar-placeholder" wx:else>
            <text>👤</text>
          </view>
        </view>

        <!-- 基本信息 -->
        <view class="engineer-basic">
          <view class="engineer-name">{{item.name}}</view>
          <view class="engineer-experience">{{item.experienceYears}}年经验 | {{item.education || '专业技师'}}</view>
          <view class="engineer-rating">
            <view class="stars">
              <text class="star" wx:for="{{item.ratingStars}}" wx:key="*this">⭐</text>
            </view>
            <text class="rating-text">{{item.rating}}分 ({{item.totalOrders}}单)</text>
          </view>
        </view>

        <!-- 操作按钮 -->
        <view class="engineer-actions">
          <view class="contact-btn" 
                catchtap="contactEngineer" 
                data-phone="{{item.phone}}" 
                data-name="{{item.name}}">
            联系
          </view>
        </view>
      </view>

      <!-- 工程师简介 -->
      <view class="engineer-bio" wx:if="{{item.bio || item.introduction}}">
        {{item.bio || item.introduction}}
      </view>

      <!-- 专业领域标签 -->
      <view class="engineer-specialties" wx:if="{{item.specialties && item.specialties.length > 0}}">
        <text class="specialty-tag" wx:for="{{item.specialties}}" wx:key="*this" wx:if="{{index < 5}}">{{item}}</text>
        <text class="specialty-tag more-tag" wx:if="{{item.specialties.length > 5}}">+{{item.specialties.length - 5}}</text>
      </view>

      <!-- 工程师统计信息 -->
      <view class="engineer-stats">
        <view class="stat-item">
          <text class="stat-label">完成订单</text>
          <text class="stat-value">{{item.completedOrders || 0}}</text>
        </view>
        <view class="stat-item">
          <text class="stat-label">成功率</text>
          <text class="stat-value">{{item.successRate || 100}}%</text>
        </view>
        <view class="stat-item" wx:if="{{item.hourlyRate}}">
          <text class="stat-label">时薪</text>
          <text class="stat-value">¥{{item.hourlyRate}}</text>
        </view>
        <view class="stat-item" wx:if="{{item.serviceFee}}">
          <text class="stat-label">服务费</text>
          <text class="stat-value">¥{{item.serviceFee}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部安全区域 -->
  <view class="safe-area"></view>
</view>
