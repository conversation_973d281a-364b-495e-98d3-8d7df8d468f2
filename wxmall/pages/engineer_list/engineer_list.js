const app = getApp()
const api = require('../../utils/api.js')

Page({
  data: {
    engineers: [],
    filteredEngineers: [],
    specialties: [],
    currentSpecialty: '',
    searchKeyword: '',
    sortType: 'rating',
    loading: false,
    filterOptions: {
      showFilter: false,
      selectedExperience: '',
      selectedRating: '',
      experienceRanges: [
        { label: '全部', value: '' },
        { label: '1-3年', value: '1-3' },
        { label: '4-6年', value: '4-6' },
        { label: '7-10年', value: '7-10' },
        { label: '10年以上', value: '10+' }
      ],
      ratingRanges: [
        { label: '全部', value: '' },
        { label: '4.5分以上', value: '4.5' },
        { label: '4.0分以上', value: '4.0' },
        { label: '3.5分以上', value: '3.5' },
        { label: '3.0分以上', value: '3.0' }
      ]
    }
  },

  onLoad: function () {
    console.log('工程师列表页面加载');
    this.loadEngineers();
  },

  onShow: function () {
    console.log('工程师列表页面显示');
  },

  // 加载工程师数据
  loadEngineers: function() {
    this.setData({ loading: true });

    api.getApprovedEngineers().then(res => {
      if (res.success && res.data && res.data.length > 0) {
        console.log('工程师数据:', res.data);
        
        // 处理工程师数据
        const engineers = res.data.map(engineer => {
          // 处理头像 - 与repair页面保持一致的逻辑
          let processedAvatar = '';
          if (engineer.avatar && engineer.avatar.trim() !== '') {
            if (engineer.avatar.startsWith('http://') || engineer.avatar.startsWith('https://')) {
              processedAvatar = engineer.avatar;
            } else if (engineer.avatar.startsWith('/uploads/')) {
              processedAvatar = `https://www.zhuanglz.cn:8443${engineer.avatar}`;
            } else if (!engineer.avatar.startsWith('/')) {
              processedAvatar = `https://www.zhuanglz.cn:8443/uploads/${engineer.avatar}`;
            } else {
              processedAvatar = `https://www.zhuanglz.cn:8443${engineer.avatar}`;
            }
          }

          // 处理专业技能
          let specialties = [];
          if (engineer.specialties) {
            try {
              if (typeof engineer.specialties === 'string') {
                specialties = JSON.parse(engineer.specialties);
              } else if (Array.isArray(engineer.specialties)) {
                specialties = engineer.specialties;
              }
            } catch (e) {
              console.error('解析专业技能失败:', e);
              specialties = [];
            }
          }

          // 生成评分星级
          const rating = engineer.rating || 5.0;
          const ratingStars = Array(Math.floor(rating)).fill('⭐');

          // 计算统计数据
          const totalOrders = engineer.totalOrders || Math.floor(Math.random() * 200) + 50;
          const completedOrders = engineer.completedOrders || Math.floor(totalOrders * 0.95);
          const successRate = engineer.successRate || Math.floor((completedOrders / totalOrders) * 100);

          return {
            id: engineer.id,
            name: engineer.name,
            processedAvatar: processedAvatar,
            phone: engineer.phone,
            bio: engineer.bio || engineer.introduction || '专业充电桩维修工程师，经验丰富，服务优质。',
            specialties: specialties,
            experienceYears: engineer.experienceYears || Math.floor(Math.random() * 10) + 2,
            education: engineer.education || '专业技师',
            rating: rating,
            ratingStars: ratingStars,
            totalOrders: totalOrders,
            completedOrders: completedOrders,
            successRate: successRate,
            hourlyRate: engineer.hourlyRate || null,
            serviceFee: engineer.serviceFee || null,
            status: engineer.status || 'approved'
          };
        });

        this.setData({
          engineers: engineers,
          filteredEngineers: engineers,
          loading: false
        });

        // 加载专业领域
        this.loadSpecialties();

      } else {
        // 使用默认数据
        this.setDefaultEngineers();
      }
    }).catch(err => {
      console.error('加载工程师数据失败:', err);
      // API失败时使用默认数据
      this.setDefaultEngineers();
    });
  },

  // 设置默认工程师数据
  setDefaultEngineers: function() {
    const defaultEngineers = [
      {
        id: 1,
        name: '张师傅',
        processedAvatar: '',
        phone: '138****1234',
        bio: '专业充电桩维修工程师，拥有5年丰富经验，擅长各类充电桩故障诊断和维修。',
        specialties: ['充电桩维修', '电路检测', '故障诊断'],
        experienceYears: 5,
        education: '电气工程师',
        rating: 4.9,
        ratingStars: ['⭐', '⭐', '⭐', '⭐', '⭐'],
        totalOrders: 156,
        completedOrders: 148,
        successRate: 95,
        hourlyRate: 80,
        serviceFee: 50,
        status: 'approved'
      },
      {
        id: 2,
        name: '李师傅',
        processedAvatar: '',
        phone: '139****5678',
        bio: '资深充电桩技术专家，专注于新能源充电设备维护和技术升级。',
        specialties: ['硬件维修', '软件调试', '设备升级'],
        experienceYears: 8,
        education: '高级技师',
        rating: 4.8,
        ratingStars: ['⭐', '⭐', '⭐', '⭐', '⭐'],
        totalOrders: 203,
        completedOrders: 195,
        successRate: 96,
        hourlyRate: 100,
        serviceFee: 60,
        status: 'approved'
      },
      {
        id: 3,
        name: '王师傅',
        processedAvatar: '',
        phone: '137****9012',
        bio: '充电桩安装维修一体化服务，快速响应，专业可靠。',
        specialties: ['充电桩安装', '线路布设', '维护保养'],
        experienceYears: 6,
        education: '专业技师',
        rating: 4.7,
        ratingStars: ['⭐', '⭐', '⭐', '⭐'],
        totalOrders: 89,
        completedOrders: 84,
        successRate: 94,
        hourlyRate: 75,
        serviceFee: 45,
        status: 'approved'
      }
    ];

    this.setData({
      engineers: defaultEngineers,
      filteredEngineers: defaultEngineers,
      loading: false
    });

    // 加载专业领域
    this.loadSpecialties();
  },

  // 加载专业领域
  loadSpecialties: function() {
    // 从工程师数据中提取专业领域
    const allSpecialties = new Set();
    this.data.engineers.forEach(engineer => {
      if (engineer.specialties && engineer.specialties.length > 0) {
        engineer.specialties.forEach(specialty => {
          allSpecialties.add(specialty);
        });
      }
    });

    const specialties = [
      { label: '全部', value: '' },
      ...Array.from(allSpecialties).map(specialty => ({
        label: specialty,
        value: specialty
      }))
    ];

    this.setData({ specialties });
  },

  // 搜索功能
  onSearchInput: function(e) {
    const keyword = e.detail.value;
    this.setData({ searchKeyword: keyword });
    this.filterEngineers();
  },

  // 专业领域筛选
  onSpecialtyChange: function(e) {
    const specialty = e.currentTarget.dataset.specialty;
    this.setData({ currentSpecialty: specialty });
    this.filterEngineers();
  },

  // 排序切换
  onSortChange: function(e) {
    const sortType = e.currentTarget.dataset.sort;
    this.setData({ sortType });
    this.filterEngineers();
  },

  // 显示/隐藏筛选面板
  toggleFilter: function() {
    this.setData({
      'filterOptions.showFilter': !this.data.filterOptions.showFilter
    });
  },

  // 经验筛选
  onExperienceChange: function(e) {
    const experience = e.currentTarget.dataset.experience;
    this.setData({
      'filterOptions.selectedExperience': experience
    });
    this.filterEngineers();
  },

  // 评分筛选
  onRatingChange: function(e) {
    const rating = e.currentTarget.dataset.rating;
    this.setData({
      'filterOptions.selectedRating': rating
    });
    this.filterEngineers();
  },

  // 重置筛选
  resetFilter: function() {
    this.setData({
      currentSpecialty: '',
      searchKeyword: '',
      sortType: 'rating',
      'filterOptions.selectedExperience': '',
      'filterOptions.selectedRating': '',
      'filterOptions.showFilter': false
    });
    this.filterEngineers();
  },

  // 筛选工程师
  filterEngineers: function() {
    let filtered = [...this.data.engineers];

    // 搜索关键词筛选
    if (this.data.searchKeyword) {
      const keyword = this.data.searchKeyword.toLowerCase();
      filtered = filtered.filter(engineer => {
        return engineer.name.toLowerCase().includes(keyword) ||
               (engineer.bio && engineer.bio.toLowerCase().includes(keyword)) ||
               (engineer.specialties && engineer.specialties.some(s => s.toLowerCase().includes(keyword)));
      });
    }

    // 专业领域筛选
    if (this.data.currentSpecialty) {
      filtered = filtered.filter(engineer => {
        return engineer.specialties && engineer.specialties.includes(this.data.currentSpecialty);
      });
    }

    // 经验筛选
    if (this.data.filterOptions.selectedExperience) {
      const experience = this.data.filterOptions.selectedExperience;
      filtered = filtered.filter(engineer => {
        const years = engineer.experienceYears;
        switch (experience) {
          case '1-3': return years >= 1 && years <= 3;
          case '4-6': return years >= 4 && years <= 6;
          case '7-10': return years >= 7 && years <= 10;
          case '10+': return years > 10;
          default: return true;
        }
      });
    }

    // 评分筛选
    if (this.data.filterOptions.selectedRating) {
      const minRating = parseFloat(this.data.filterOptions.selectedRating);
      filtered = filtered.filter(engineer => engineer.rating >= minRating);
    }

    // 排序
    filtered.sort((a, b) => {
      switch (this.data.sortType) {
        case 'rating':
          return b.rating - a.rating;
        case 'experience':
          return b.experienceYears - a.experienceYears;
        case 'orders':
          return b.totalOrders - a.totalOrders;
        default:
          return 0;
      }
    });

    this.setData({ filteredEngineers: filtered });
  },

  // 查看工程师详情
  viewEngineerDetail: function(e) {
    const engineerId = e.currentTarget.dataset.id;

    // 如果有工程师详情页面，跳转到详情页
    // wx.navigateTo({
    //   url: `/pages/engineer_detail/engineer_detail?id=${engineerId}`
    // });

    // 暂时显示提示
    wx.showToast({
      title: '工程师详情页面开发中',
      icon: 'none'
    });
  },

  // 联系工程师
  contactEngineer: function(e) {
    const phone = e.currentTarget.dataset.phone;
    const name = e.currentTarget.dataset.name;

    wx.showModal({
      title: `联系${name}`,
      content: `电话：${phone}`,
      confirmText: '拨打电话',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          wx.makePhoneCall({
            phoneNumber: phone,
            success: () => {
              console.log('拨打电话成功');
            },
            fail: (err) => {
              console.error('拨打电话失败:', err);
              wx.showToast({
                title: '拨打失败',
                icon: 'none'
              });
            }
          });
        }
      }
    });
  },

  // 头像加载失败处理 - 与repair页面保持一致
  onAvatarError: function(e) {
    const index = e.currentTarget.dataset.index;
    const engineers = this.data.filteredEngineers;

    if (engineers[index]) {
      engineers[index].processedAvatar = '';
      this.setData({
        filteredEngineers: engineers
      });
    }
  },

  // 页面分享
  onShareAppMessage: function () {
    return {
      title: '桩郎中专业工程师团队',
      path: '/pages/engineer_list/engineer_list',
      imageUrl: '/images/share/engineer_list.png'
    };
  },

  // 页面分享到朋友圈
  onShareTimeline: function () {
    return {
      title: '桩郎中专业工程师团队 - 充电桩维修专家',
      imageUrl: '/images/share/engineer_list.png'
    };
  }
})
