.container {
  padding: 30rpx 30rpx 120rpx;
  background-color: #f5f5f5;
}

/* 搜索栏 */
.search-bar {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
  gap: 16rpx;
}

.search-input-wrapper {
  flex: 1;
  position: relative;
  background-color: white;
  border-radius: 32rpx;
  padding: 20rpx 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.search-input {
  width: 100%;
  font-size: 28rpx;
  border: none;
  background: transparent;
  padding-right: 60rpx;
}

.search-icon {
  position: absolute;
  right: 24rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 28rpx;
  color: #999;
}

.filter-btn {
  display: flex;
  align-items: center;
  background-color: white;
  padding: 20rpx 24rpx;
  border-radius: 32rpx;
  font-size: 28rpx;
  color: #333;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.filter-icon {
  margin-left: 8rpx;
  font-size: 24rpx;
  color: #666;
}

/* 专业领域标签 */
.specialty-tabs {
  margin-bottom: 24rpx;
  white-space: nowrap;
}

.specialty-tab-wrapper {
  display: inline-flex;
  gap: 16rpx;
  padding: 0 8rpx;
}

.specialty-tab {
  display: inline-block;
  padding: 16rpx 32rpx;
  background-color: white;
  border-radius: 32rpx;
  font-size: 26rpx;
  color: #666;
  white-space: nowrap;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
}

.specialty-tab.active {
  background-color: #1e88e5;
  color: white;
}

/* 排序栏 */
.sort-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: white;
  padding: 24rpx 32rpx;
  border-radius: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.sort-options {
  display: flex;
  gap: 32rpx;
}

.sort-option {
  font-size: 28rpx;
  color: #666;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  transition: all 0.3s;
}

.sort-option.active {
  background-color: #e3f2fd;
  color: #1e88e5;
  font-weight: 500;
}

.result-count {
  font-size: 26rpx;
  color: #999;
}

/* 筛选面板 */
.filter-panel {
  background-color: white;
  border-radius: 24rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
  max-height: 0;
  opacity: 0;
  transition: all 0.3s;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.filter-panel.show {
  max-height: 800rpx;
  opacity: 1;
  padding: 32rpx;
}

.filter-section {
  margin-bottom: 32rpx;
}

.filter-section:last-of-type {
  margin-bottom: 0;
}

.filter-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.filter-option {
  padding: 12rpx 24rpx;
  background-color: #f5f5f5;
  border-radius: 20rpx;
  font-size: 26rpx;
  color: #666;
  transition: all 0.3s;
}

.filter-option.active {
  background-color: #1e88e5;
  color: white;
}

.filter-actions {
  display: flex;
  gap: 24rpx;
  margin-top: 32rpx;
  padding-top: 32rpx;
  border-top: 1px solid #e0e0e0;
}

.filter-reset,
.filter-confirm {
  flex: 1;
  text-align: center;
  padding: 20rpx;
  border-radius: 32rpx;
  font-size: 28rpx;
  font-weight: 500;
}

.filter-reset {
  background-color: #f5f5f5;
  color: #666;
}

.filter-confirm {
  background-color: #1e88e5;
  color: white;
}

/* 加载状态 */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #e0e0e0;
  border-top: 4rpx solid #1e88e5;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 24rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading text {
  font-size: 28rpx;
  color: #666;
}

/* 空状态 */
.empty {
  text-align: center;
  padding: 100rpx 40rpx;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
}

.empty-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 32rpx;
}

.empty-action {
  background-color: #1e88e5;
  color: white;
  padding: 20rpx 40rpx;
  border-radius: 32rpx;
  font-size: 28rpx;
  display: inline-block;
}

/* 工程师列表 */
.engineer-list {
  margin-bottom: 32rpx;
}

.engineer-card {
  background-color: white;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
  transition: transform 0.2s, box-shadow 0.2s;
}

.engineer-card:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* 工程师头部 */
.engineer-header {
  display: flex;
  align-items: flex-start;
  margin-bottom: 24rpx;
}

/* 头像容器 - 与repair页面保持一致 */
.engineer-avatar-container {
  width: 120rpx;
  height: 120rpx;
  margin-right: 24rpx;
  position: relative;
  flex-shrink: 0;
}

.engineer-avatar {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 48rpx;
}

/* 基本信息 */
.engineer-basic {
  flex: 1;
}

.engineer-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.engineer-experience {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 12rpx;
}

.engineer-rating {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.stars {
  display: flex;
}

.star {
  font-size: 24rpx;
}

.rating-text {
  font-size: 24rpx;
  color: #666;
}

/* 操作按钮 */
.engineer-actions {
  flex-shrink: 0;
}

.contact-btn {
  background-color: #1e88e5;
  color: white;
  padding: 16rpx 24rpx;
  border-radius: 20rpx;
  font-size: 26rpx;
  text-align: center;
  min-width: 80rpx;
}

/* 工程师简介 */
.engineer-bio {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 20rpx;
}

/* 专业领域标签 */
.engineer-specialties {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-bottom: 20rpx;
}

.specialty-tag {
  background-color: #f0f0f0;
  color: #666;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 24rpx;
}

.more-tag {
  background-color: #e3f2fd;
  color: #1e88e5;
}

/* 统计信息 */
.engineer-stats {
  display: flex;
  justify-content: space-between;
  padding-top: 20rpx;
  border-top: 1px solid #f0f0f0;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.stat-label {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.stat-value {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

/* 底部安全区域 */
.safe-area {
  height: 40rpx;
}
