/* 页面容器 */
.container {
  padding: 0;
  background-color: #f8f9fa;
  min-height: 100vh;
}

/* 成功状态头部 */
.success-header {
  text-align: center;
  padding: 80rpx 40rpx 60rpx;
  background: linear-gradient(135deg, #1890ff, #096dd9);
  color: white;
  position: relative;
}

.success-icon-bg {
  width: 120rpx;
  height: 120rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  margin: 0 auto 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.success-icon {
  font-size: 60rpx;
  font-weight: bold;
  color: white;
}

.success-title {
  font-size: 44rpx;
  font-weight: 600;
  margin-bottom: 20rpx;
}

.success-subtitle {
  font-size: 28rpx;
  opacity: 0.9;
  line-height: 1.6;
  max-width: 600rpx;
  margin: 0 auto;
}

/* 信息卡片 */
.info-card {
  background-color: white;
  margin: 30rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.info-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.info-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.info-status {
  background: #fff7e6;
  color: #fa8c16;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.info-content {
  padding: 30rpx;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24rpx;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 28rpx;
  color: #666;
  flex-shrink: 0;
  width: 160rpx;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  flex: 1;
  text-align: right;
  word-break: break-all;
}

/* 工程师推荐部分 */
.engineer-section {
  background-color: white;
  margin: 30rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.engineer-card {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-top: 1rpx solid #f0f0f0;
}

.engineer-avatar-container {
  position: relative;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.engineer-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  overflow: hidden;
}

.engineer-avatar-placeholder {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-text {
  color: white;
  font-size: 36rpx;
  font-weight: 600;
}

.online-status {
  position: absolute;
  bottom: 4rpx;
  right: 4rpx;
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  border: 2rpx solid white;
}

.online-status.online {
  background-color: #52c41a;
}

.online-status.offline {
  background-color: #d9d9d9;
}

.engineer-info {
  flex: 1;
}

.engineer-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.engineer-details {
  display: flex;
  gap: 16rpx;
  margin-bottom: 8rpx;
}

.engineer-rating {
  font-size: 24rpx;
  color: #ff9800;
}

.engineer-experience {
  font-size: 24rpx;
  color: #666;
}

.engineer-specialty {
  font-size: 24rpx;
  color: #1e88e5;
}

.engineer-actions {
  flex-shrink: 0;
}

.engineer-contact {
  flex-shrink: 0;
}

.contact-btn {
  background: linear-gradient(135deg, #1890ff, #096dd9);
  color: white;
  border: none;
  border-radius: 24rpx;
  padding: 16rpx 24rpx;
  font-size: 26rpx;
  font-weight: 500;
  min-width: 100rpx;
  text-align: center;
}

/* 加载状态 */
.engineer-loading {
  padding: 60rpx 30rpx;
  text-align: center;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 3rpx solid #f0f0f0;
  border-top: 3rpx solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

.no-engineer {
  padding: 60rpx 30rpx;
  text-align: center;
}

.no-engineer-text {
  font-size: 28rpx;
  color: #999;
}

/* 操作按钮区域 */
.action-section {
  padding: 30rpx;
  background: white;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.action-btn {
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 600;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn::after {
  border: none;
}

.primary-btn {
  background: linear-gradient(135deg, #1890ff, #096dd9);
  color: white;
  box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.3);
}

.secondary-btn {
  background: #f8f9fa;
  color: #666;
  border: 2rpx solid #e9ecef;
}

/* 推荐产品区域 */
.products-section {
  background: white;
  margin: 30rpx;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.products-grid {
  display: flex;
  gap: 20rpx;
  overflow-x: auto;
  padding-bottom: 10rpx;
}

.product-item {
  background: #f8f9fa;
  border-radius: 12rpx;
  overflow: hidden;
  flex-shrink: 0;
  width: 280rpx;
}

.product-image {
  width: 100%;
  height: 160rpx;
}

.product-info {
  padding: 20rpx;
}

.product-name {
  font-size: 26rpx;
  color: #333;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-price {
  font-size: 28rpx;
  font-weight: 600;
  color: #ff4d4f;
}

/* 温馨提示区域 */
.tips-section {
  background: white;
  margin: 30rpx;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.tips-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.tips-list {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 24rpx;
  border-left: 4rpx solid #1890ff;
}

.tip-item {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 16rpx;
  position: relative;
  padding-left: 20rpx;
}

.tip-item:before {
  content: '•';
  color: #1890ff;
  position: absolute;
  left: 0;
  font-weight: bold;
}

.tip-item:last-child {
  margin-bottom: 0;
}

/* 通用样式 */
.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}
