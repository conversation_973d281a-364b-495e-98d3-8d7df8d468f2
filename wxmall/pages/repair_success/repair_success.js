const app = getApp()

Page({
  data: {
    appointmentId: '',
    faultTypeName: '',
    appointmentTime: '',
    address: '',
    orderData: {},
    isLoadingEngineer: true,
    engineerInfo: {},
    recommendProducts: []
  },

  onLoad: function (options) {
    console.log('维修成功页面加载:', options);
    
    // 获取维修信息
    this.getRepairInfo();
    
  },

  // 获取维修信息
  getRepairInfo: function() {
    const repairInfo = app.globalData.repairInfo || {};
    const orderId = app.globalData.repairOrderId || app.globalData.repairOrderNo || 'R' + Date.now();
    const orderData = app.globalData.repairOrder || {};

    // 故障类型映射
    const faultTypeMap = {
      'no_charging': '无法充电',
      'slow_charging': '充电慢',
      'error_code': '报错代码',
      'port_damage': '接口损坏',
      'not_starting': '无法启动',
      'overheating': '过热',
      'display_issue': '显示故障',
      'other': '其他故障'
    };

    this.setData({
      appointmentId: orderId,
      faultTypeName: faultTypeMap[repairInfo.faultType] || '未知故障',
      appointmentTime: `${repairInfo.appointmentDate} ${repairInfo.appointmentTime}`,
      address: repairInfo.fullAddress || '未填写地址',
      orderData: orderData
    });

  },

 
  
  // 返回首页
  goToHome: function() {
    // 使用reLaunch方法跳转到首页，并关闭所有其他页面
    wx.reLaunch({
      url: '/pages/index/index',
      success: () => {
        console.log('成功跳转到首页');
      },
      fail: (err) => {
        console.error('跳转首页失败:', err);
      }
    });
  },

  // 跳转到订单详情
  goToOrderDetail: function() {
    if (!this.data.appointmentId) {
      wx.showToast({
        title: '订单信息不完整',
        icon: 'none'
      });
      return;
    }

    wx.navigateTo({
      url: `/pages/repair_detail/repair_detail?id=${this.data.appointmentId}`,
      success: () => {
        console.log('成功跳转到订单详情');
      },
      fail: (err) => {
        console.error('跳转订单详情失败:', err);
        wx.showToast({
          title: '跳转失败，请重试',
          icon: 'none'
        });
      }
    });
  },

  // 跳转到维修列表
  goToRepairList: function() {
    wx.navigateTo({
      url: '/pages/repair_list/repair_list'
    });
  },

  

})
