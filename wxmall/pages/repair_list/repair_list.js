// 严格按照抖音小程序repair_list模板设计
const app = getApp()

Page({
  data: {
    status: 'pending',
    orders: [],
    statusText: {
      pending: '待接单',
      accepted: '待上门',
      processing: '维修中',
      completed: '已完成'
    },
    faultTypeMap: {
      no_charging: '无法充电',
      slow_charging: '充电慢',
      error_code: '报错代码',
      port_damage: '接口损坏',
      not_starting: '无法启动',
      overheating: '过热',
      display_issue: '显示故障',
      other: '其他故障'
    }
  },

  onLoad: function (options) {
    // 如果有状态参数，设置默认选中的标签页
    if (options.status) {
      this.setData({
        status: options.status
      });
    }

    this.loadOrders();
  },

  onShow: function () {
    this.loadOrders();
  },

  onPullDownRefresh: function () {
    this.loadOrders();
  },

  // 切换标签页
  switchTab: function(e) {
    const status = e.currentTarget.dataset.status;
    this.setData({
      status: status
    });
    this.loadOrders();
  },

  // 加载订单列表
  loadOrders: function() {
    const openId = app.globalData.openId || wx.getStorageSync('openId');

    if (!openId) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    console.log('🔍 开始加载维修订单列表, openId:', openId, 'status:', this.data.status);

    wx.showLoading({
      title: '加载中...'
    });

    const api = require('../../utils/api');

    // 调用后端API获取真实订单数据
    api.getRepairOrderList(openId, this.data.status)
      .then(res => {
        wx.hideLoading();
        console.log('📊 API返回的订单列表数据:', res);

        if (res.success && res.orders) {
          // 处理订单数据
          const processedOrders = res.orders.map(order => this.processOrderData(order));

          console.log('✅ 处理后的订单数据:', processedOrders);

          this.setData({
            orders: processedOrders
          });
        } else {
          console.warn('⚠️ API返回数据格式异常:', res);
          this.setData({
            orders: []
          });

          if (res.message) {
            wx.showToast({
              title: res.message,
              icon: 'none'
            });
          }
        }
      })
      .catch(err => {
        wx.hideLoading();
        console.error('❌ 获取维修订单列表失败:', err);

        this.setData({
          orders: []
        });

        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      })
      .finally(() => {
        // 停止下拉刷新
        wx.stopPullDownRefresh();
      });
  },

  // 处理订单数据
  processOrderData: function(orderData) {
    // 处理图片数组
    let images = [];
    if (orderData.images) {
      try {
        images = typeof orderData.images === 'string'
          ? JSON.parse(orderData.images)
          : orderData.images;

        // 处理图片URL
        images = images.map(img => this.processImageUrl(img));
      } catch (e) {
        console.error('解析图片数据失败:', e);
        images = [];
      }
    }

    // 格式化时间
    const createdAtFormatted = this.formatDateTime(orderData.createdAt);
    const appointmentTimeFormatted = `${orderData.appointmentDate} ${orderData.appointmentTime}`;

    return {
      ...orderData,
      images: images,
      createdAtFormatted: createdAtFormatted,
      appointmentTime: appointmentTimeFormatted,
      faultTypeName: this.data.faultTypeMap[orderData.faultType] || '未知故障'
    };
  },

  // 处理图片URL
  processImageUrl: function(imageUrl) {
    if (!imageUrl || imageUrl.trim() === '') {
      return '';
    }

    // 如果已经是完整的HTTP/HTTPS URL，直接返回
    if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
      return imageUrl;
    }

    // 如果是相对路径，添加服务器地址
    if (imageUrl.startsWith('/uploads/')) {
      return `https://localhost:8443${imageUrl}`;
    }

    // 如果只是文件名，添加完整路径
    if (!imageUrl.startsWith('/')) {
      return `https://localhost:8443/uploads/${imageUrl}`;
    }

    // 其他情况，添加服务器地址
    return `https://localhost:8443${imageUrl}`;
  },

  // 格式化日期时间
  formatDateTime: function(dateTime) {
    if (!dateTime) return '';

    const date = new Date(dateTime);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}`;
  },

  // 跳转到订单详情
  goToOrderDetail: function(e) {
    const orderNo = e.currentTarget.dataset.id;
    console.log('🔍 跳转到订单详情, orderNo:', orderNo);

    if (!orderNo) {
      wx.showToast({
        title: '订单号不能为空',
        icon: 'none'
      });
      return;
    }

    wx.navigateTo({
      url: `/pages/repair_detail/repair_detail?orderNo=${orderNo}`,
      fail: (err) => {
        console.error('❌ 跳转到订单详情失败:', err);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },

  // 取消订单
  cancelOrder: function(e) {
    const orderNo = e.currentTarget.dataset.id;
    const openId = app.globalData.openId || wx.getStorageSync('openId');

    if (!openId) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    console.log('🔍 准备取消订单, orderNo:', orderNo, 'openId:', openId);

    wx.showModal({
      title: '确认取消',
      content: '确定要取消这个维修订单吗？',
      success: (res) => {
        if (res.confirm) {
          // 显示加载状态
          wx.showLoading({
            title: '取消中...'
          });

          const api = require('../../utils/api');

          // 调用取消订单API（使用与抖音小程序一致的参数名）
          api.cancelRepairOrder(orderNo, openId)
            .then(response => {
              wx.hideLoading();
              console.log('✅ 取消订单API响应:', response);

              if (response.success) {
                wx.showToast({
                  title: '取消成功',
                  icon: 'success'
                });

                // 重新加载订单列表
                this.loadOrders();
              } else {
                wx.showToast({
                  title: response.message || '取消失败',
                  icon: 'none'
                });
              }
            })
            .catch(err => {
              wx.hideLoading();
              console.error('❌ 取消订单失败:', err);

              wx.showToast({
                title: '网络错误，请重试',
                icon: 'none'
              });
            });
        }
      }
    });
  },

  // 联系客服
  contactService: function() {
    wx.navigateTo({
      url: '/pages/customer_service/customer_service'
    });
  }
})
