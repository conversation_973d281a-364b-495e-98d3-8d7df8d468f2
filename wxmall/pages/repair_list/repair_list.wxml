<!-- 严格按照抖音小程序repair_list模板设计 -->
<view class="container">
  <view class="header">
    <view class="tab-bar">
      <view class="tab {{status === 'pending' ? 'active' : ''}}" bindtap="switchTab" data-status="pending">待接单</view>
      <view class="tab {{status === 'accepted' ? 'active' : ''}}" bindtap="switchTab" data-status="accepted">待上门</view>
      <view class="tab {{status === 'processing' ? 'active' : ''}}" bindtap="switchTab" data-status="processing">维修中</view>
      <view class="tab {{status === 'completed' ? 'active' : ''}}" bindtap="switchTab" data-status="completed">已完成</view>
    </view>
  </view>

  <view class="content">
    <block wx:if="{{orders.length > 0}}">
      <view class="order-list">
        <view class="order-item" wx:for="{{orders}}" wx:key="id" bindtap="goToOrderDetail" data-id="{{item.orderNo}}">
          <view class="order-header">
            <view class="order-no">订单号: {{item.orderNo}}</view>
            <view class="order-status">
              <text class="status-text status-{{item.status}}">{{statusText[item.status]}}</text>
            </view>
          </view>
          <view class="order-info">
            <view class="info-item">
              <text class="label">故障类型:</text>
              <text class="value">{{faultTypeMap[item.faultType] || '未知故障'}}</text>
            </view>
            <view class="info-item">
              <text class="label">充电桩型号:</text>
              <text class="value">{{item.model}}</text>
            </view>
            <view class="info-item">
              <text class="label">预约时间:</text>
              <text class="value">{{item.appointmentTime}}</text>
            </view>
            <view class="info-item">
              <text class="label">服务地址:</text>
              <text class="value address">{{item.fullAddress}}</text>
            </view>
          </view>
          <view class="order-footer">
            <view class="time">创建时间: {{item.createdAtFormatted}}</view>
            <view class="actions">
              <button class="btn btn-primary" wx:if="{{item.status === 'pending'}}" catchtap="cancelOrder" data-id="{{item.orderNo}}">取消订单</button>
              <button class="btn btn-default" catchtap="contactService">联系客服</button>
            </view>
          </view>
        </view>
      </view>
    </block>
    <view class="empty-state" wx:else>
      <image class="empty-icon" src="/images/icons/empty.png" mode="aspectFit"></image>
      <text class="empty-text">暂无{{statusText[status]}}订单</text>
    </view>
  </view>
</view>
