/* 严格按照抖音小程序repair_list模板设计 */
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header {
  background-color: #fff;
  position: sticky;
  top: 0;
  z-index: 10;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
}

.tab-bar {
  display: flex;
  justify-content: space-around;
  padding: 20rpx 0;
}

.tab {
  padding: 16rpx 24rpx;
  font-size: 28rpx;
  color: #666;
  position: relative;
}

.tab.active {
  color: #1890ff;
  font-weight: 500;
}

.tab.active::after {
  content: '';
  position: absolute;
  bottom: -20rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 6rpx;
  background-color: #1890ff;
  border-radius: 6rpx;
}

.content {
  flex: 1;
  padding: 30rpx;
}

.order-list {
  margin-bottom: 40rpx;
}

.order-item {
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #eee;
}

.order-no {
  font-size: 28rpx;
  color: #666;
}

.order-status {
  font-size: 28rpx;
}

.status-text {
  padding: 6rpx 16rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
}

.status-pending {
  background-color: #e6f7ff;
  color: #1890ff;
}

.status-accepted {
  background-color: #fff7e6;
  color: #fa8c16;
}

.status-processing {
  background-color: #f6ffed;
  color: #52c41a;
}

.status-completed {
  background-color: #f9f9f9;
  color: #8c8c8c;
}

.order-info {
  padding: 20rpx 0;
}

.info-item {
  display: flex;
  margin-bottom: 16rpx;
}

.label {
  width: 160rpx;
  color: #999;
  font-size: 26rpx;
}

.value {
  flex: 1;
  color: #333;
  font-size: 26rpx;
}

.address {
  word-break: break-all;
}

.order-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20rpx;
  border-top: 2rpx solid #eee;
}

.time {
  font-size: 24rpx;
  color: #999;
}

.actions {
  display: flex;
}

.btn {
  margin-left: 20rpx;
  font-size: 24rpx;
  padding: 8rpx 24rpx;
  border-radius: 8rpx;
}

.btn-primary {
  background-color: #1890ff;
  color: #fff;
  border: none;
}

.btn-default {
  background-color: #fff;
  color: #666;
  border: 2rpx solid #d9d9d9;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  color: #999;
  font-size: 28rpx;
  margin-bottom: 40rpx;
}
