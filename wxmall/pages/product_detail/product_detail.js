const app = getApp()
const api = require('../../utils/api.js')

Page({
  data: {
    id: null,
    product: null,
    currentTab: 0,
    showSpec: false,
    selectedSpecId: null,
    selectedSpec: '',
    quantity: 1,
    isFavorite: false
  },

  onLoad: function (options) {
    const id = parseInt(options.id);
    this.setData({
      id: id
    });

    // 获取商品详情
    this.loadProductDetail(id);

    // 获取购物车数量
    this.getCartCount();

    // 检查收藏状态
    this.checkFavoriteStatus(id);
  },

  onShow: function () {
    // 页面显示时更新购物车数量
    this.getCartCount();
  },

  // 加载商品详情
  loadProductDetail: function(id) {
    wx.showLoading({
      title: '加载中'
    });

    api.getProductDetail(id).then(res => {
      wx.hideLoading();

      if (res.success && res.data) {
        console.log('API返回的数据:', res.data);

        // 处理商品数据 - 修复：应该使用 res.data.product
        let product = this.processProductData(res.data.product || res.data);

        console.log('处理后的商品数据:', product);

        this.setData({
          product: product
        });

        console.log('页面数据设置完成，当前data:', this.data);

      } else {
        // 如果API失败，使用默认商品数据
        console.log('API失败，使用默认数据');
        this.setDefaultProductData(id);
      }
    }).catch(err => {
      wx.hideLoading();
      console.error('加载商品详情失败:', err);

      // API失败时使用默认数据
      this.setDefaultProductData(id);
    });
  },

  // 设置默认商品数据
  setDefaultProductData: function(id) {
    const defaultProducts = {
      1: {
        id: 1,
        name: '快充充电枪',
        price: 299.00,
        originalPrice: 399.00,
        mainImage: '/images/products/快充充电枪.png',
        description: '高品质快充充电枪，安全可靠，适用于各种电动车型。',
        stock: 100,
        sales: 256,
        brand: '桩郎中',
        model: 'ZLZ-001',
        weight: '2.5',
        dimensions: '30×15×10cm',
        warrantyPeriod: '2年',
        features: ['快速充电', '安全可靠', '兼容性强', '耐用材质'],
        specifications: [
          { name: '品牌', value: '桩郎中' },
          { name: '型号', value: 'ZLZ-001' },
          { name: '功率', value: '7KW' },
          { name: '电压', value: '220V' },
          { name: '电流', value: '32A' },
          { name: '线缆长度', value: '5米' },
          { name: '防护等级', value: 'IP54' },
          { name: '工作温度', value: '-30℃~+50℃' }
        ]
      },
      2: {
        id: 2,
        name: '充电线缆',
        price: 199.00,
        originalPrice: 299.00,
        mainImage: '/images/products/充电线缆.png',
        description: '优质充电线缆，导电性能优异，使用寿命长。',
        stock: 200,
        sales: 189,
        brand: '桩郎中',
        model: 'ZLZ-002'
      }
    };

    const product = defaultProducts[id] || defaultProducts[1];

    this.setData({
      product: this.processProductData(product)
    });
  },

  // 处理商品数据
  processProductData: function(product) {
    if (!product) return {};

    // 处理图片数据
    product = this.processProductImages(product);

    // 处理JSON字段
    const jsonFields = ['features', 'specifications', 'specs', 'services'];
    jsonFields.forEach(field => {
      if (product[field] && typeof product[field] === 'string') {
        try {
          product[field] = JSON.parse(product[field]);
        } catch (e) {
          console.error(`解析${field}字段失败:`, e);
          product[field] = [];
        }
      }
    });

    return product;
  },

  // 处理商品图片
  processProductImages: function(product) {
    // 处理主图
    if (product.mainImage && !product.mainImage.startsWith('http')) {
      product.mainImage = `/images/products/${product.mainImage}`;
    }

    // 处理详情图
    if (product.detailImage && !product.detailImage.startsWith('http')) {
      product.detailImage = `/images/products/${product.detailImage}`;
    }

    // 处理图片数组
    if (product.images && Array.isArray(product.images)) {
      product.images = product.images.map(img => {
        if (img && !img.startsWith('http')) {
          return `/images/products/${img}`;
        }
        return img;
      });
    } else if (product.mainImage) {
      // 如果没有图片数组，使用主图
      product.images = [product.mainImage];
    }

    return product;
  },



  // 获取购物车数量
  getCartCount: function() {
    const cart = wx.getStorageSync('cart') || [];
    const cartCount = cart.reduce((total, item) => total + item.quantity, 0);
    this.setData({ cartCount });
  },

  // 检查收藏状态
  checkFavoriteStatus: function(productId) {
    const favorites = wx.getStorageSync('favorites') || [];
    const isFavorite = favorites.some(item => item.id === productId);
    this.setData({ isFavorite });
  },

  // 切换选项卡
  switchTab: function(e) {
    const tab = parseInt(e.currentTarget.dataset.tab);
    this.setData({ currentTab: tab });
  },

  // 显示规格选择器
  showSpecSelector: function() {
    this.setData({ showSpec: true });
  },

  // 隐藏规格选择器
  hideSpecSelector: function() {
    this.setData({ showSpec: false });
  },

  // 阻止冒泡
  preventBubble: function() {
    // 阻止事件冒泡
  },

  // 选择规格
  selectSpec: function(e) {
    const { id, name } = e.currentTarget.dataset;
    this.setData({
      selectedSpecId: id,
      selectedSpec: name
    });
  },

  // 增加数量
  increaseQuantity: function() {
    this.setData({
      quantity: this.data.quantity + 1
    });
  },

  // 减少数量
  decreaseQuantity: function() {
    if (this.data.quantity > 1) {
      this.setData({
        quantity: this.data.quantity - 1
      });
    }
  },

  // 数量输入
  onQuantityInput: function(e) {
    const value = parseInt(e.detail.value) || 1;
    this.setData({
      quantity: Math.max(1, value)
    });
  },

  // 确认规格
  confirmSpec: function() {
    this.setData({ showSpec: false });
  },

  // 切换收藏
  toggleFavorite: function() {
    const favorites = wx.getStorageSync('favorites') || [];
    const product = this.data.product;

    if (this.data.isFavorite) {
      // 取消收藏
      const newFavorites = favorites.filter(item => item.id !== product.id);
      wx.setStorageSync('favorites', newFavorites);
      wx.showToast({
        title: '已取消收藏',
        icon: 'success'
      });
    } else {
      // 添加收藏
      const favoriteItem = {
        id: product.id,
        name: product.name,
        price: product.price,
        image: product.mainImage,
        addTime: new Date().getTime()
      };
      favorites.push(favoriteItem);
      wx.setStorageSync('favorites', favorites);
      wx.showToast({
        title: '已添加收藏',
        icon: 'success'
      });
    }

    this.setData({
      isFavorite: !this.data.isFavorite
    });
  },

  // 跳转到购物车
  goToCart: function() {
    wx.switchTab({
      url: '/pages/cart/cart'
    });
  },

  // 加入购物车
  addToCart: function() {
    const product = this.data.product;
    if (!product) return;

    const cartItem = {
      id: product.id,
      name: product.name,
      price: product.price,
      image: product.mainImage,
      quantity: this.data.quantity,
      spec: this.data.selectedSpec || '',
      specId: this.data.selectedSpecId
    };

    let cart = wx.getStorageSync('cart') || [];

    // 检查是否已存在相同商品和规格
    const existingIndex = cart.findIndex(item =>
      item.id === cartItem.id && item.specId === cartItem.specId
    );

    if (existingIndex > -1) {
      cart[existingIndex].quantity += cartItem.quantity;
    } else {
      cart.push(cartItem);
    }

    wx.setStorageSync('cart', cart);
    this.getCartCount();

    wx.showToast({
      title: '已加入购物车',
      icon: 'success'
    });
  },

  // 立即购买
  buyNow: function() {
    wx.showToast({
      title: '购买功能暂未开放，请等待营业执照办理完成',
      icon: 'none',
      duration: 2000
    });
  },

  // 预览图片
  previewImage: function(e) {
    const url = e.currentTarget.dataset.url;
    const urls = e.currentTarget.dataset.urls || [url];

    wx.previewImage({
      current: url,
      urls: urls
    });
  }
})

