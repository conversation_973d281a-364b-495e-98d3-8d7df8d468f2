.container {
  padding-bottom: 100rpx;
}

.product-image {
  width: 100%;
  height: 600rpx;
}

.product-image image {
  width: 100%;
  height: 100%;
}

.product-info {
  padding: 32rpx;
  background-color: white;
  margin-bottom: 24rpx;
}

.product-price {
  font-size: 48rpx;
  color: #f44336;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.original-price {
  font-size: 32rpx;
  color: #999;
  text-decoration: line-through;
  font-weight: normal;
}

.product-title {
  font-size: 36rpx;
  font-weight: 600;
  margin: 24rpx 0;
}

.product-subtitle {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.product-sales {
  color: #757575;
  font-size: 28rpx;
  display: flex;
  gap: 32rpx;
}

.product-brand {
  color: #757575;
  font-size: 28rpx;
  margin-top: 16rpx;
  display: flex;
  gap: 32rpx;
}

.card {
  background-color: white;
  padding: 32rpx;
  margin-bottom: 24rpx;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
}

.text-primary {
  color: #1e88e5;
  font-size: 28rpx;
}

.text-xs {
  font-size: 24rpx;
  color: #757575;
}

.text-light {
  color: #757575;
}

.text-center {
  text-align: center;
}

.text-sm {
  font-size: 28rpx;
}

.arrow-icon {
  font-size: 24rpx;
  color: #757575;
  margin-left: 8rpx;
}

.divider {
  height: 1px;
  background-color: #e0e0e0;
  margin: 8rpx 0;
}

.badge {
  display: inline-block;
  padding: 4rpx 12rpx;
  margin-right: 8rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
}

.badge-outline {
  border: 1px solid #e0e0e0;
  color: #666;
}

.tabs {
  display: flex;
  background-color: white;
  border-bottom: 1px solid #e0e0e0;
}

.tab {
  flex: 1;
  text-align: center;
  padding: 32rpx 0;
  font-size: 28rpx;
  color: #666;
  position: relative;
}

.tab.active {
  color: #1e88e5;
  font-weight: 500;
}

.tab.active:after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 4rpx;
  background-color: #1e88e5;
}

.tab-content {
  padding: 32rpx;
  background-color: white;
}

.detail-section {
  margin-bottom: 32rpx;
}

.detail-title {
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 16rpx;
}

.detail-item {
  margin-bottom: 12rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.feature-icon {
  color: #4caf50;
  font-weight: bold;
  font-size: 32rpx;
}

.detail-text {
  font-size: 28rpx;
  line-height: 1.6;
}

.spec-table {
  border: 1px solid #e0e0e0;
  border-radius: 12rpx;
  overflow: hidden;
}

.spec-row {
  display: flex;
  border-bottom: 1px solid #e0e0e0;
}

.spec-row:last-child {
  border-bottom: none;
}

.spec-name {
  width: 200rpx;
  padding: 20rpx;
  background-color: #f5f5f5;
  font-weight: 500;
  font-size: 28rpx;
}

.spec-value {
  flex: 1;
  padding: 20rpx;
  font-size: 28rpx;
}



.font-bold {
  font-weight: 600;
}

.rating {
  margin: 8rpx 0;
}

.gap-3 {
  gap: 24rpx;
}

.flex {
  display: flex;
}

.mt-2 {
  margin-top: 16rpx;
}

.mb-3 {
  margin-bottom: 24rpx;
}



.product-actions {
  display: flex;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  border-top: 1px solid #e0e0e0;
  z-index: 100;
}

.action-btn {
  flex: 1;
  padding: 24rpx;
  text-align: center;
  font-size: 28rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}

.cart-badge {
  position: absolute;
  top: 10rpx;
  right: 30rpx;
  background-color: #f44336;
  color: white;
  font-size: 20rpx;
  min-width: 32rpx;
  height: 32rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 6rpx;
}

.buy-now {
  background-color: #f44336;
  color: white;
}

.add-to-cart {
  background-color: #1e88e5;
  color: white;
}

/* 规格选择器 */
.spec-selector {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
  visibility: hidden;
  opacity: 0;
  transition: all 0.3s;
}

.spec-selector.show {
  visibility: visible;
  opacity: 1;
}

.spec-content {
  background-color: white;
  border-radius: 24rpx 24rpx 0 0;
  width: 100%;
  max-height: 80vh;
  overflow-y: auto;
  transform: translateY(100%);
  transition: transform 0.3s;
}

.spec-selector.show .spec-content {
  transform: translateY(0);
}

.spec-header {
  padding: 32rpx;
  display: flex;
  position: relative;
  border-bottom: 1px solid #e0e0e0;
}

.spec-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 12rpx;
  margin-right: 24rpx;
}

.spec-info {
  flex: 1;
}

.close-btn {
  position: absolute;
  top: 32rpx;
  right: 32rpx;
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: #f5f5f5;
}

.spec-body {
  padding: 32rpx;
}

.spec-group {
  margin-bottom: 32rpx;
}

.spec-group-title {
  font-weight: 500;
  margin-bottom: 16rpx;
}

.spec-options {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.spec-option {
  padding: 12rpx 24rpx;
  border: 1px solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
}

.spec-option.selected {
  border-color: #1e88e5;
  background-color: #e3f2fd;
  color: #1e88e5;
}

.quantity-selector {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.quantity-btn {
  width: 60rpx;
  height: 60rpx;
  border: 1px solid #e0e0e0;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #333;
  border-radius: 8rpx;
}

.quantity-input {
  width: 120rpx;
  height: 60rpx;
  text-align: center;
  border: 1px solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
}

.spec-footer {
  padding: 32rpx;
  border-top: 1px solid #e0e0e0;
}

.btn {
  background-color: #1e88e5;
  color: white;
  border: none;
  border-radius: 32rpx;
  padding: 24rpx;
  font-size: 32rpx;
  font-weight: 500;
}

.btn-block {
  width: 100%;
}
  

.action-btn.favorited {
  color: #f44336;
}

.btn-icon {
  font-size: 32rpx;
  margin-bottom: 4rpx;
}

.btn-text {
  font-size: 20rpx;
}

.action-right {
  flex: 1;
  display: flex;
  gap: 16rpx;
}

.cart-btn {
  flex: 1;
  background-color: #ff9800;
  color: white;
  border: none;
  border-radius: 32rpx;
  padding: 24rpx;
  font-size: 28rpx;
  font-weight: 500;
}

.buy-btn {
  flex: 1;
  background-color: #f44336;
  color: white;
  border: none;
  border-radius: 32rpx;
  padding: 24rpx;
  font-size: 28rpx;
  font-weight: 500;
}
