<view class="container">
  <!-- 调试信息 -->
  <view style="background: #f0f0f0; padding: 20rpx; margin: 20rpx; font-size: 24rpx;" wx:if="{{!product}}">
    <text>商品数据为空，正在加载...</text>
  </view>

  <view style="background: #e8f5e8; padding: 20rpx; margin: 20rpx; font-size: 24rpx;" wx:if="{{product}}">
    <text>商品数据已加载: {{product.name}}</text>
  </view>

  <!-- 商品图片 -->
  <swiper class="product-image" indicator-dots="{{true}}" autoplay="{{true}}" interval="{{3000}}" duration="{{500}}" wx:if="{{product && product.images && product.images.length > 0}}">
    <swiper-item wx:for="{{product.images}}" wx:key="index">
      <image src="{{item}}" mode="aspectFill" lazy-load="{{true}}" />
    </swiper-item>
  </swiper>

  <!-- 默认图片（当没有轮播图时） -->
  <view class="product-image" wx:elif="{{product}}">
    <image src="{{product.mainImage || '/images/products/充电线缆.png'}}" mode="aspectFill" />
  </view>

  <!-- 商品信息 -->
  <view class="product-info" wx:if="{{product}}">
    <view class="product-price">
      ¥{{product.price || '0.00'}}
      <text class="original-price" wx:if="{{product.originalPrice && product.originalPrice > product.price}}">¥{{product.originalPrice}}</text>
    </view>
    <view class="product-title">{{product.name || '商品名称'}}</view>
    <view class="product-subtitle" wx:if="{{product.shortName}}">{{product.shortName}}</view>
    <view class="product-sales">
      <text>销量: {{product.sales || 0}}</text>
      <text wx:if="{{product.stock !== undefined}}">库存: {{product.stock}}</text>
    </view>
    <view class="product-brand" wx:if="{{product.brand}}">
      <text>品牌: {{product.brand}}</text>
      <text wx:if="{{product.model}}">型号: {{product.model}}</text>
    </view>
  </view>

  <!-- 规格选择 -->
  <view class="card">
    <view class="flex-between" bindtap="showSpecSelector" wx:if="{{product.specs && product.specs.length > 0}}">
      <view>规格</view>
      <view class="text-primary">
        {{selectedSpec ? selectedSpec : '选择'}}
        <text class="arrow-icon">→</text>
      </view>
    </view>
    <view class="divider" wx:if="{{product.specs && product.specs.length > 0 && product.services && product.services.length > 0}}"></view>
    <view class="flex-between" wx:if="{{product.services && product.services.length > 0}}">
      <view>服务</view>
      <view class="text-xs">
        <text class="badge badge-outline" wx:for="{{product.services}}" wx:key="*this">{{item}}</text>
      </view>
    </view>
    <!-- 当没有规格和服务时显示基本信息 -->
    <view wx:if="{{(!product.specs || product.specs.length === 0) && (!product.services || product.services.length === 0)}}">
      <view class="flex-between" wx:if="{{product.weight}}">
        <view>重量</view>
        <view class="text-xs">{{product.weight}}kg</view>
      </view>
      <view class="divider" wx:if="{{product.weight && product.dimensions}}"></view>
      <view class="flex-between" wx:if="{{product.dimensions}}">
        <view>尺寸</view>
        <view class="text-xs">{{product.dimensions}}</view>
      </view>
      <view class="divider" wx:if="{{(product.weight || product.dimensions) && product.warrantyPeriod}}"></view>
      <view class="flex-between" wx:if="{{product.warrantyPeriod}}">
        <view>保修期</view>
        <view class="text-xs">{{product.warrantyPeriod}}</view>
      </view>
    </view>
  </view>

  <!-- 选项卡 -->
  <view class="tabs">
    <view class="tab {{currentTab === 0 ? 'active' : ''}}" bindtap="switchTab" data-tab="0">商品详情</view>
    <view class="tab {{currentTab === 1 ? 'active' : ''}}" bindtap="switchTab" data-tab="1">规格参数</view>
  </view>

  <!-- 选项卡内容 -->
  <view class="tab-content" hidden="{{currentTab !== 0}}">
    <!-- 产品描述 -->
    <view class="detail-section" wx:if="{{product.description}}">
      <view class="detail-title">产品描述</view>
      <view class="detail-text">{{product.description}}</view>
    </view>

    <!-- 详情图片 -->
    <view class="text-center mb-3" wx:if="{{product.detailImage}}">
      <image src="{{product.detailImage}}" mode="widthFix" style="width: 100%;" lazy-load="{{true}}" />
    </view>

    <!-- 产品特点 -->
    <view class="detail-section" wx:if="{{product.features && product.features.length > 0}}">
      <view class="detail-title">产品特点</view>
      <view class="detail-item" wx:for="{{product.features}}" wx:key="index">
        <text class="feature-icon">✓</text> {{item}}
      </view>
    </view>

    <!-- 适用车型 -->
    <view class="detail-section" wx:if="{{product.compatibleCars}}">
      <view class="detail-title">适用车型</view>
      <view class="detail-text">{{product.compatibleCars}}</view>
    </view>

    <!-- 当没有详情内容时的提示 -->
    <view class="detail-section" wx:if="{{!product.description && !product.detailImage && (!product.features || product.features.length === 0) && !product.compatibleCars}}">
      <view class="text-center text-light">
        <text>暂无详细信息</text>
      </view>
    </view>
  </view>

  <view class="tab-content" hidden="{{currentTab !== 1}}">
    <!-- 规格参数表格 -->
    <view class="spec-table" wx:if="{{product.specifications && product.specifications.length > 0}}">
      <view class="spec-row" wx:for="{{product.specifications}}" wx:key="name">
        <view class="spec-name">{{item.name}}</view>
        <view class="spec-value">{{item.value}}</view>
      </view>
    </view>

    <!-- 基本参数（当没有详细规格时） -->
    <view class="spec-table" wx:if="{{!product.specifications || product.specifications.length === 0}}">
      <view class="spec-row" wx:if="{{product.brand}}">
        <view class="spec-name">品牌</view>
        <view class="spec-value">{{product.brand}}</view>
      </view>
      <view class="spec-row" wx:if="{{product.model}}">
        <view class="spec-name">型号</view>
        <view class="spec-value">{{product.model}}</view>
      </view>
      <view class="spec-row" wx:if="{{product.weight}}">
        <view class="spec-name">重量</view>
        <view class="spec-value">{{product.weight}}kg</view>
      </view>
      <view class="spec-row" wx:if="{{product.dimensions}}">
        <view class="spec-name">尺寸</view>
        <view class="spec-value">{{product.dimensions}}</view>
      </view>
      <view class="spec-row" wx:if="{{product.warrantyPeriod}}">
        <view class="spec-name">保修期</view>
        <view class="spec-value">{{product.warrantyPeriod}}</view>
      </view>
    </view>

    <!-- 当没有任何规格信息时的提示 -->
    <view class="text-center text-light" wx:if="{{(!product.specifications || product.specifications.length === 0) && !product.brand && !product.model && !product.weight && !product.dimensions && !product.warrantyPeriod}}">
      <text>暂无规格参数</text>
    </view>
  </view>



  <!-- 底部操作栏 -->
  <view class="product-actions">
    <view class="action-btn" bindtap="toggleFavorite">
      <text style="color: {{isFavorite ? '#ff4757' : '#666'}}">{{isFavorite ? '❤️' : '🤍'}}</text>
      <text>{{isFavorite ? '已收藏' : '收藏'}}</text>
    </view>
    <view class="action-btn" bindtap="goToCart">
      <text>🛒</text>
      <text>购物车</text>
      <view class="cart-badge" wx:if="{{cartCount > 0}}">{{cartCount}}</view>
    </view>
    <view class="action-btn add-to-cart" bindtap="addToCart">加入购物车</view>
    <view class="action-btn buy-now" bindtap="buyNow">立即购买</view>
  </view>

  <!-- 规格选择弹窗 -->
  <view class="spec-selector {{showSpec ? 'show' : ''}}" bindtap="hideSpecSelector">
    <view class="spec-content" catchtap="preventBubble">
      <view class="spec-header">
        <image src="{{product.mainImage}}" class="spec-image" mode="aspectFill" />
        <view class="spec-info">
          <view class="product-price">¥{{product.price}}</view>
          <view class="text-sm">库存: {{product.stock}}件</view>
          <view class="text-sm">已选: {{selectedSpec ? selectedSpec : '请选择规格'}}</view>
        </view>
        <view class="close-btn" bindtap="hideSpecSelector">
          <text>✕</text>
        </view>
      </view>

      <view class="spec-body">
        <view class="spec-group">
          <view class="spec-group-title">规格</view>
          <view class="spec-options">
            <view class="spec-option {{selectedSpecId === item.id ? 'selected' : ''}}"
                  wx:for="{{product.specs}}"
                  wx:key="id"
                  bindtap="selectSpec"
                  data-id="{{item.id}}"
                  data-name="{{item.name}}">
              {{item.name}}
            </view>
          </view>
        </view>

        <view class="spec-group">
          <view class="spec-group-title">数量</view>
          <view class="quantity-selector">
            <view class="quantity-btn" bindtap="decreaseQuantity">-</view>
            <input type="number" class="quantity-input" value="{{quantity}}" bindinput="onQuantityInput" />
            <view class="quantity-btn" bindtap="increaseQuantity">+</view>
          </view>
        </view>
      </view>

      <view class="spec-footer">
        <button class="btn btn-block" bindtap="confirmSpec">确定</button>
      </view>
    </view>
  </view>
</view>
