<view class="container">
  <!-- Banner -->
  <swiper class="banner" indicator-dots="{{true}}" autoplay="{{true}}" interval="{{3000}}" duration="{{500}}">
    <swiper-item wx:for="{{banners}}" wx:key="index">
      <image src="{{item.image}}" mode="aspectFill" />
    </swiper-item>
  </swiper>

  <!-- 维修选项 -->
  <view class="section-title">
    <text>选择服务方式</text>
  </view>

  <view wx:for="{{serviceOptions}}" wx:key="type" class="repair-option" bindtap="goToServiceOption" data-type="{{item.type}}">
    <view class="repair-icon">
      <image src="{{item.image}}" mode="aspectFit" style="width: 60rpx; height: 60rpx;"></image>
    </view>
    <view class="repair-info">
      <view class="repair-title">{{item.title}}</view>
      <view class="repair-desc">{{item.desc}}</view>
    </view>
    <text class="arrow-icon">→</text>
  </view>

  <!-- 常见故障 -->
  <view class="section-title mt-3">
    <text>常见故障类型</text>
  </view>

  <view class="card">
    <view class="fault-grid">
      <view class="fault-type" wx:for="{{faultTypes}}" wx:key="id" bindtap="goToRepairForm" data-type="{{item.type}}">
        <view class="fault-icon">
          <image src="{{item.image}}" mode="aspectFit" style="width: 60rpx; height: 60rpx;"></image>
        </view>
        <view class="fault-name">{{item.name}}</view>
      </view>
    </view>
  </view>

  <!-- 维修价格 -->
  <view class="section-title mt-3">
    <text>维修价格参考</text>
  </view>

  <view class="card">
    <view class="price-table">
      <view class="price-row" wx:for="{{priceList}}" wx:key="name">
        <view class="price-name">{{item.name}}</view>
        <view class="price-value">¥{{item.price}}</view>
      </view>
    </view>
    <view class="text-xs text-light mt-2">注：实际价格以技师现场检测为准，上门检测费用¥50（维修后免除）</view>
  </view>

  <!-- 技师团队 -->
  <view class="section-title mt-3">
    <text>专业技师团队</text>
    <view class="text-xs text-light">共{{engineerStats.approved}}名认证技师</view>
  </view>

  <view class="card">
    <view class="engineer" wx:for="{{engineers}}" wx:key="id" wx:if="{{index < 3}}">
      <view class="engineer-avatar-container">
        <image
          src="{{item.processedAvatar}}"
          class="engineer-avatar"
          mode="aspectFill"
          binderror="onAvatarError"
          data-index="{{index}}"
          wx:if="{{item.processedAvatar}}"
        />
        <view class="avatar-placeholder" wx:if="{{!item.processedAvatar}}">
          <text>👤</text>
        </view>
      </view>
      <view class="engineer-info">
        <view class="engineer-name">{{item.name}}</view>
        <view class="rating">
          <text wx:for="{{item.rating}}" wx:key="*this">⭐</text>
          <text class="text-xs text-light ml-1">{{item.ratingValue}} ({{item.reviewCount}}次服务)</text>
        </view>
        <view class="text-xs text-light">{{item.experience}} | {{item.title}}</view>
        <view class="text-xs text-light" wx:if="{{item.bio}}">{{item.bio}}</view>
        <view class="engineer-tags" wx:if="{{item.specialties && item.specialties.length > 0}}">
          <text class="tag" wx:for="{{item.specialties}}" wx:key="*this" wx:if="{{index < 3}}">{{item}}</text>
        </view>
      </view>
    </view>

    <view class="text-center mt-3" wx:if="{{engineers.length > 3}}">
      <text class="text-primary text-sm" bindtap="viewAllEngineers">查看全部{{engineers.length}}名技师 ></text>
    </view>
  </view>
</view>
