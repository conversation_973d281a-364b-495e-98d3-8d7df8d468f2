const app = getApp()
const { imageManager } = require('../../utils/imageManager');

Page({
  data: {
    banners: [], // 动态加载轮播图
    faultTypes: [
      { id: 1, name: '无法充电', type: 'no_charging', image: '' },
      { id: 2, name: '充电慢', type: 'slow_charging', image: '' },
      { id: 3, name: '报错代码', type: 'error_code', image: '' },
      { id: 4, name: '接口损坏', type: 'port_damage', image: '' },
      { id: 5, name: '无法启动', type: 'not_starting', image: '' },
      { id: 6, name: '过热', type: 'overheating', image: '' },
      { id: 7, name: '显示故障', type: 'display_issue', image: '' },
      { id: 8, name: '其他故障', type: 'other', image: '' }
    ],
    priceList: [
      { name: '基础检测', price: '50' },
      { name: '软件故障', price: '100-200' },
      { name: '硬件更换', price: '200-500' },
      { name: '电路维修', price: '300-800' },
      { name: '主板维修', price: '500-1200' }
    ],
    engineers: [],
    engineerStats: {
      approved: 0
    },
    serviceOptions: [
      { type: 'home', title: '上门维修', desc: '专业技师上门，快速解决充电桩故障', image: '' },
      { type: 'center', title: '网点维修', desc: '就近选择服务网点，专业设备保障', image: '' },
      { type: 'remote', title: '远程指导', desc: '在线专家指导，解决简单故障问题', image: '' }
    ]
  },
  onLoad: function () {

    this.loadImageResources();
    this.loadEngineers();
  },

  onShow: function () {
    this.loadEngineers();
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 2
      })
    }
  },

  // 加载图片资源
  loadImageResources: function() {
    // 加载轮播图
    this.loadBanners();

    // 加载故障类型图标
    this.loadFaultTypeIcons();

    // 加载服务方式图标
    this.loadServiceOptionIcons();
  },

  // 加载轮播图
  loadBanners: function() {
    imageManager.getBanners('repair').then(banners => {
      // 转换为页面需要的格式
      const formattedBanners = banners.map((banner, index) => ({
        id: banner.id || index + 1,
        image: banner.url,
        name: banner.name
      }));

      this.setData({
        banners: formattedBanners
      });
    }).catch(error => {

      // 使用默认轮播图
      this.setData({
        banners: [
          { image: imageManager.getDefaultImageUrl('banner'), name: '默认轮播图' }
        ]
      });
    });
  },

  // 加载故障类型图标
  loadFaultTypeIcons: function() {
    imageManager.getIcons().then(icons => {
      // 更新faultTypes中的图标
      const faultTypes = this.data.faultTypes.map(fault => {
        // 根据类型匹配图标
        const iconName = this.getFaultIconNameByType(fault.type);
        const icon = icons.find(item =>
          item.name.includes(iconName) ||
          item.name.includes(fault.name)
        );

        return {
          ...fault,
          image: icon ? icon.url : imageManager.getDefaultImageUrl('icon')
        };
      });

      this.setData({
        faultTypes: faultTypes
      });
    }).catch(error => {

      // 使用默认图标
      const faultTypes = this.data.faultTypes.map(fault => ({
        ...fault,
        image: imageManager.getDefaultImageUrl('icon')
      }));

      this.setData({
        faultTypes: faultTypes
      });
    });
  },

  // 加载服务方式图标
  loadServiceOptionIcons: function() {
    imageManager.getIcons().then(icons => {
      // 更新serviceOptions中的图标
      const serviceOptions = this.data.serviceOptions.map(option => {
        // 根据类型匹配图标
        const iconName = this.getServiceIconNameByType(option.type);
        const icon = icons.find(item =>
          item.name.includes(iconName) ||
          item.name.includes(option.title)
        );

        return {
          ...option,
          image: icon ? icon.url : imageManager.getDefaultImageUrl('icon')
        };
      });

      this.setData({
        serviceOptions: serviceOptions
      });
    }).catch(error => {

      // 使用默认图标
      const serviceOptions = this.data.serviceOptions.map(option => ({
        ...option,
        image: imageManager.getDefaultImageUrl('icon')
      }));

      this.setData({
        serviceOptions: serviceOptions
      });
    });
  },

  // 根据服务类型获取图标名称
  getServiceIconNameByType: function(type) {
    const serviceIconMap = {
      home: '上门维修',
      center: '网点维修',
      remote: '远程指导'
    };
    return serviceIconMap[type] || type;
  },

  // 根据故障类型获取图标名称
  getFaultIconNameByType: function(type) {
    const faultIconMap = {
      no_charging: '无法充电',
      slow_charging: '充电慢',
      error_code: '错误代码',
      port_damage: '接口损坏',
      not_starting: '无法启动',
      overheating: '过热',
      display_issue: '显示故障',
      other: '其他故障'
    };
    return faultIconMap[type] || type;
  },

  // 加载工程师数据
  loadEngineers: function() {
    const api = require('../../utils/api');

    api.getApprovedEngineers().then(res => {
      if (res.success && res.data) {
        const engineers = res.data.map(engineer => {
          // 处理头像
          let processedAvatar = '';
          if (engineer.avatar && engineer.avatar.trim() !== '') {
            if (engineer.avatar.startsWith('http://') || engineer.avatar.startsWith('https://')) {
              processedAvatar = engineer.avatar;
            } else if (engineer.avatar.startsWith('/uploads/')) {
              processedAvatar = `https://www.zhuanglz.cn:8443${engineer.avatar}`;
            } else if (!engineer.avatar.startsWith('/')) {
              processedAvatar = `https://www.zhuanglz.cn:8443/uploads/${engineer.avatar}`;
            } else {
              processedAvatar = `https://www.zhuanglz.cn:8443${engineer.avatar}`;
            }
          }

          // 处理专业技能
          let specialties = [];
          if (engineer.specialties) {
            try {
              if (typeof engineer.specialties === 'string') {
                specialties = JSON.parse(engineer.specialties);
              } else if (Array.isArray(engineer.specialties)) {
                specialties = engineer.specialties;
              }
            } catch (e) {

              specialties = [];
            }
          }

          // 生成评分
          const ratingValue = (4.5 + Math.random() * 0.5).toFixed(1);
          const rating = Array(Math.floor(parseFloat(ratingValue))).fill('⭐');
          const reviewCount = Math.floor(Math.random() * 200) + 50;

          return {
            ...engineer,
            processedAvatar,
            specialties,
            rating,
            ratingValue,
            reviewCount,
            experience: `${Math.floor(Math.random() * 8) + 2}年经验`,
            title: '认证工程师'
          };
        });

        this.setData({
          engineers: engineers,
          engineerStats: {
            approved: engineers.length
          }
        });
      } else {
        // 使用模拟数据
        this.setMockEngineers();
      }
    }).catch(err => {

      this.setMockEngineers();
    });
  },

  // 设置模拟工程师数据
  setMockEngineers: function() {
    const mockEngineers = [
      {
        id: 1,
        name: '张师傅',
        processedAvatar: '/images/engineers/engineer1.png',
        rating: ['⭐', '⭐', '⭐', '⭐', '⭐'],
        ratingValue: '4.9',
        reviewCount: 156,
        experience: '5年经验',
        title: '高级工程师',
        bio: '专业充电桩维修，经验丰富',
        specialties: ['充电桩维修', '电路检测', '故障诊断']
      },
      {
        id: 2,
        name: '李师傅',
        processedAvatar: '/images/engineers/engineer2.png',
        rating: ['⭐', '⭐', '⭐', '⭐'],
        ratingValue: '4.7',
        reviewCount: 89,
        experience: '3年经验',
        title: '认证工程师',
        bio: '快速响应，服务周到',
        specialties: ['硬件维修', '软件调试']
      },
      {
        id: 3,
        name: '王师傅',
        processedAvatar: '/images/engineers/engineer3.png',
        rating: ['⭐', '⭐', '⭐', '⭐', '⭐'],
        ratingValue: '4.8',
        reviewCount: 203,
        experience: '6年经验',
        title: '资深工程师',
        bio: '技术精湛，解决疑难故障',
        specialties: ['主板维修', '系统升级', '预防性维护']
      }
    ];

    this.setData({
      engineers: mockEngineers,
      engineerStats: {
        approved: mockEngineers.length
      }
    });
  },

  // 头像加载失败处理
  onAvatarError: function(e) {
    const index = e.currentTarget.dataset.index;
    const engineers = this.data.engineers;
    engineers[index].processedAvatar = '';
    this.setData({ engineers });
  },

  // 统一的服务选项点击处理
  goToServiceOption: function(e) {
    const type = e.currentTarget.dataset.type;
    switch (type) {
      case 'home':
        this.goToRepairForm(e);
        break;
      case 'center':
        this.goToServiceCenters();
        break;
      case 'remote':
        this.goToOnlineConsult();
        break;
      default:

    }
  },

  // 跳转到维修表单
  goToRepairForm: function(e) {
    const type = e.currentTarget.dataset.type;
    let url = '/pages/repair_form/repair_form';
    if (type) {
      url += `?type=${type}`;
    }
    wx.navigateTo({
      url: url
    });
  },

  // 跳转到服务网点
  goToServiceCenters: function() {
    wx.navigateTo({
      url: '/pages/service_centers/service_centers'
    });
  },

  // 跳转到在线咨询
  goToOnlineConsult: function() {
    wx.navigateTo({
      url: '/pages/customer_service/customer_service'
    });
  },

  // 查看所有工程师
  viewAllEngineers: function() {
    wx.navigateTo({
      url: '/pages/engineer_list/engineer_list'
    });
  }
})
