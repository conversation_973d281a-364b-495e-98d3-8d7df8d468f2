<scroll-view class="container" scroll-y="{{true}}">
  <!-- 申请须知 -->
  <view class="notice">
    <view class="notice-title">
      <text>ℹ️</text>
      <text>申请须知</text>
    </view>
    <view class="notice-content">
      <text>• 请确保提供的信息真实有效</text>
      <text>• 相关资质证书必须清晰可见</text>
      <text>• 审核周期为3-5个工作日</text>
      <text>• 如有疑问请联系客服：18382412473</text>
    </view>
  </view>

  <!-- 基本信息 -->
  <view class="form-section">
    <view class="section-title">基本信息</view>

    <view class="form-item">
      <view class="form-label">姓名 <text class="required">*</text></view>
      <input
        class="form-input"
        placeholder="请输入真实姓名"
        value="{{formData.name}}"
        data-field="name"
        bindinput="onInputChange"
      />
    </view>

    <view class="form-item">
      <view class="form-label">联系电话 <text class="required">*</text></view>
      <input
        class="form-input"
        placeholder="请输入联系电话"
        type="number"
        value="{{formData.phone}}"
        data-field="phone"
        bindinput="onInputChange"
      />
    </view>

    <view class="form-item">
      <view class="form-label">邮箱地址</view>
      <input
        class="form-input"
        placeholder="请输入邮箱地址"
        value="{{formData.email}}"
        data-field="email"
        bindinput="onInputChange"
      />
    </view>

    <view class="form-item">
      <view class="form-label">身份证号 <text class="required">*</text></view>
      <input
        class="form-input"
        placeholder="请输入身份证号"
        value="{{formData.idCard}}"
        data-field="idCard"
        bindinput="onInputChange"
      />
    </view>

    <view class="form-item">
      <view class="form-label">年龄</view>
      <input
        class="form-input"
        placeholder="请输入年龄"
        type="number"
        value="{{formData.age}}"
        data-field="age"
        bindinput="onInputChange"
      />
    </view>
  </view>

  <!-- 工作经验 -->
  <view class="form-section">
    <view class="section-title">工作经验</view>

    <view class="form-item">
      <view class="form-label">工作年限 <text class="required">*</text></view>
      <input
        class="form-input"
        placeholder="请输入工作年限（年）"
        type="number"
        value="{{formData.experienceYears}}"
        data-field="experienceYears"
        bindinput="onInputChange"
      />
    </view>

    <view class="form-item">
      <view class="form-label">教育背景</view>
      <input
        class="form-input"
        placeholder="如：大专、本科、技校等"
        value="{{formData.education}}"
        data-field="education"
        bindinput="onInputChange"
      />
    </view>

    <view class="form-item">
      <view class="form-label">专业技能 <text class="required">*</text></view>
      <view class="checkbox-group">
        <view
          class="checkbox-item {{item.checked ? 'checked' : ''}}"
          wx:for="{{specialtyOptions}}"
          wx:key="id"
          bindtap="onSpecialtyChange"
          data-index="{{index}}"
        >
          <text class="checkbox-icon">{{item.checked ? '✓' : ''}}</text>
          <text class="checkbox-text">{{item.name}}</text>
        </view>
      </view>
    </view>

    <view class="form-item">
      <view class="form-label">工作经历</view>
      <textarea
        class="form-textarea"
        placeholder="请简要描述您的工作经历和技能特长"
        value="{{formData.workExperience}}"
        data-field="workExperience"
        bindinput="onInputChange"
        maxlength="500"
      ></textarea>
    </view>
  </view>

  <!-- 服务信息 -->
  <view class="form-section">
    <view class="section-title">服务信息</view>

    <view class="form-item">
      <view class="form-label">期望时薪（元/小时）</view>
      <input
        class="form-input"
        placeholder="请输入期望时薪"
        type="digit"
        value="{{formData.hourlyRate}}"
        data-field="hourlyRate"
        bindinput="onInputChange"
      />
    </view>

    <view class="form-item">
      <view class="form-label">服务费用（元/次）</view>
      <input
        class="form-input"
        placeholder="请输入基础服务费用"
        type="digit"
        value="{{formData.serviceFee}}"
        data-field="serviceFee"
        bindinput="onInputChange"
      />
    </view>

    <view class="form-item">
      <view class="form-label">服务区域</view>
      <input
        class="form-input"
        placeholder="如：成都市高新区、锦江区等"
        value="{{formData.serviceArea}}"
        data-field="serviceArea"
        bindinput="onInputChange"
      />
    </view>

    <view class="form-item">
      <view class="form-label">可工作时间</view>
      <view class="checkbox-group">
        <view
          class="checkbox-item {{item.checked ? 'checked' : ''}}"
          wx:for="{{workTimeOptions}}"
          wx:key="id"
          bindtap="onWorkTimeChange"
          data-index="{{index}}"
        >
          <text class="checkbox-icon">{{item.checked ? '✓' : ''}}</text>
          <text class="checkbox-text">{{item.name}}</text>
        </view>
      </view>
    </view>

    <view class="form-item">
      <view class="form-label">个人简介</view>
      <textarea
        class="form-textarea"
        placeholder="请简要介绍您的个人优势和服务特色"
        value="{{formData.bio}}"
        data-field="bio"
        bindinput="onInputChange"
        maxlength="300"
      ></textarea>
    </view>
  </view>

  <!-- 个人头像 -->
  <view class="form-section">
    <view class="section-title">个人头像</view>

    <view class="form-item">
      <view class="form-label">头像照片</view>
      <view class="upload-area" bindtap="uploadAvatar">
        <image
          wx:if="{{formData.avatar}}"
          src="{{formData.avatar}}"
          class="uploaded-image"
          mode="aspectFill"
          bindtap="previewAvatar"
          data-src="{{formData.avatar}}"
        ></image>
        <view wx:else class="upload-placeholder">
          <text>📷</text>
          <text>点击上传头像</text>
        </view>
        <!-- 重新上传按钮 -->
        <view wx:if="{{formData.avatar}}" class="reupload-btn" bindtap="uploadAvatar">
          <text>📷</text>
        </view>
      </view>
      <view class="upload-tip">
        📸 建议上传清晰的个人照片作为头像
        <text style="display: block; margin-top: 5rpx; color: #666;">💡 点击图片可预览，点击相机图标可重新上传</text>
      </view>
    </view>
  </view>

  <!-- 资质证明 -->
  <view class="form-section">
    <view class="section-title">资质证明</view>

    <view class="form-item">
      <view class="form-label">身份证照片 <text class="required">*</text></view>
      <view class="upload-grid">
        <view class="upload-item" wx:if="{{formData.idCardFront}}">
          <image
            src="{{formData.idCardFront}}"
            class="uploaded-image"
            mode="aspectFill"
            bindtap="previewIdCard"
            data-src="{{formData.idCardFront}}"
            data-type="front"
          ></image>
          <view class="delete-btn" bindtap="deleteIdCard" data-type="front">
            <text>✕</text>
          </view>
          <view class="image-label">身份证正面</view>
        </view>
        <view class="upload-item upload-placeholder" wx:else bindtap="uploadIdCard" data-type="front">
          <text>📷</text>
          <text>身份证正面</text>
        </view>

        <view class="upload-item" wx:if="{{formData.idCardBack}}">
          <image
            src="{{formData.idCardBack}}"
            class="uploaded-image"
            mode="aspectFill"
            bindtap="previewIdCard"
            data-src="{{formData.idCardBack}}"
            data-type="back"
          ></image>
          <view class="delete-btn" bindtap="deleteIdCard" data-type="back">
            <text>✕</text>
          </view>
          <view class="image-label">身份证背面</view>
        </view>
        <view class="upload-item upload-placeholder" wx:else bindtap="uploadIdCard" data-type="back">
          <text>📷</text>
          <text>身份证背面</text>
        </view>
      </view>
      <view class="upload-tip">
        🆔 请上传清晰的身份证正反面照片
      </view>
    </view>

    <view class="form-item">
      <view class="form-label">资质证书</view>
      <view class="upload-grid">
        <view
          class="upload-item"
          wx:for="{{formData.qualificationCertificates}}"
          wx:key="*this"
        >
          <image
            src="{{item}}"
            class="uploaded-image"
            mode="aspectFill"
            bindtap="previewCertificate"
            data-src="{{item}}"
            data-index="{{index}}"
          ></image>
          <view class="delete-btn" bindtap="deleteCertificate" data-index="{{index}}">
            <text>✕</text>
          </view>
          <view class="image-mask">
            <text>👁️</text>
          </view>
        </view>
        <view
          class="upload-item upload-placeholder"
          bindtap="uploadCertificates"
          wx:if="{{formData.qualificationCertificates.length < 9}}"
        >
          <text>➕</text>
          <text>添加证书</text>
        </view>
      </view>
      <view class="upload-tip">
        🏆 上传相关技能证书（如电工证、技师证等）
        <text style="display: block; margin-top: 5rpx; color: #666;">💡 点击图片可预览，最多9张</text>
      </view>
    </view>
  </view>

  <!-- 提交按钮 -->
  <view class="submit-section">
    <button
      class="submit-btn {{submitting ? 'submitting' : ''}}"
      bindtap="submitApplication"
      disabled="{{submitting}}"
    >
      {{submitting ? '提交中...' : '提交申请'}}
    </button>
    <view class="submit-tip">
      提交后我们将在3-5个工作日内完成审核，请保持电话畅通
    </view>
  </view>

  <!-- 底部安全区域 -->
  <view class="safe-area"></view>
</scroll-view>
