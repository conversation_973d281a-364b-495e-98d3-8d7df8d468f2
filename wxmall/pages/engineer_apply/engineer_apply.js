const app = getApp()
const api = require('../../utils/api.js')

Page({
  data: {
    formData: {
      name: '',
      phone: '',
      email: '',
      idCard: '',
      age: '',
      experienceYears: '',
      education: '',
      workExperience: '',
      hourlyRate: '',
      serviceFee: '',
      serviceArea: '',
      bio: '',
      avatar: '',
      idCardFront: '',
      idCardBack: '',
      qualificationCertificates: []
    },
    specialtyOptions: [
      { id: 1, name: '充电桩维修', checked: false },
      { id: 2, name: '充电桩安装', checked: false },
      { id: 3, name: '电路检测', checked: false },
      { id: 4, name: '故障诊断', checked: false },
      { id: 5, name: '设备保养', checked: false },
      { id: 6, name: '技术咨询', checked: false }
    ],
    workTimeOptions: [
      { id: 1, name: '工作日', checked: false },
      { id: 2, name: '周末', checked: false },
      { id: 3, name: '节假日', checked: false },
      { id: 4, name: '夜班', checked: false },
      { id: 5, name: '随时待命', checked: false }
    ],
    submitting: false
  },

  onLoad: function () {

  },

  onShow: function () {

  },

  // 输入框变化
  onInputChange: function(e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;
    
    this.setData({
      [`formData.${field}`]: value
    });
  },

  // 专业技能变化
  onSpecialtyChange: function(e) {
    const index = e.currentTarget.dataset.index;
    const specialtyOptions = this.data.specialtyOptions;
    specialtyOptions[index].checked = !specialtyOptions[index].checked;
    
    this.setData({
      specialtyOptions: specialtyOptions
    });
  },

  // 工作时间变化
  onWorkTimeChange: function(e) {
    const index = e.currentTarget.dataset.index;
    const workTimeOptions = this.data.workTimeOptions;
    workTimeOptions[index].checked = !workTimeOptions[index].checked;
    
    this.setData({
      workTimeOptions: workTimeOptions
    });
  },

  // 上传头像
  uploadAvatar: function() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePath = res.tempFilePaths[0];
        this.uploadImageToServer(tempFilePath, 'avatar');
      },
      fail: (err) => {

      }
    });
  },

  // 预览头像
  previewAvatar: function(e) {
    const src = e.currentTarget.dataset.src;
    wx.previewImage({
      current: src,
      urls: [src]
    });
  },

  // 上传身份证
  uploadIdCard: function(e) {
    const type = e.currentTarget.dataset.type;
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePath = res.tempFilePaths[0];
        this.uploadImageToServer(tempFilePath, type === 'front' ? 'idCardFront' : 'idCardBack');
      },
      fail: (err) => {

      }
    });
  },

  // 预览身份证
  previewIdCard: function(e) {
    const src = e.currentTarget.dataset.src;
    const type = e.currentTarget.dataset.type;
    const urls = [];
    
    if (this.data.formData.idCardFront) {
      urls.push(this.data.formData.idCardFront);
    }
    if (this.data.formData.idCardBack) {
      urls.push(this.data.formData.idCardBack);
    }
    
    wx.previewImage({
      current: src,
      urls: urls
    });
  },

  // 删除身份证
  deleteIdCard: function(e) {
    const type = e.currentTarget.dataset.type;
    const field = type === 'front' ? 'idCardFront' : 'idCardBack';
    
    this.setData({
      [`formData.${field}`]: ''
    });
  },

  // 上传资质证书
  uploadCertificates: function() {
    const maxCount = 9 - this.data.formData.qualificationCertificates.length;
    if (maxCount <= 0) {
      wx.showToast({
        title: '最多上传9张证书',
        icon: 'none'
      });
      return;
    }

    wx.chooseImage({
      count: maxCount,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        res.tempFilePaths.forEach(tempFilePath => {
          this.uploadImageToServer(tempFilePath, 'qualificationCertificates');
        });
      },
      fail: (err) => {

      }
    });
  },

  // 预览证书
  previewCertificate: function(e) {
    const src = e.currentTarget.dataset.src;
    const index = e.currentTarget.dataset.index;
    wx.previewImage({
      current: src,
      urls: this.data.formData.qualificationCertificates
    });
  },

  // 删除证书
  deleteCertificate: function(e) {
    const index = e.currentTarget.dataset.index;
    const certificates = this.data.formData.qualificationCertificates;
    certificates.splice(index, 1);
    
    this.setData({
      'formData.qualificationCertificates': certificates
    });
  },

  // 上传图片到服务器
  uploadImageToServer: function(tempFilePath, field) {
    wx.showLoading({
      title: '上传中...'
    });

    wx.uploadFile({
      url: 'https://www.zhuanglz.cn:8443/api/upload/image',
      filePath: tempFilePath,
      name: 'file',
      success: (res) => {
        wx.hideLoading();
        try {
          const data = JSON.parse(res.data);
          if (data.success) {
            const imageUrl = data.data.url;
            
            if (field === 'qualificationCertificates') {
              const certificates = this.data.formData.qualificationCertificates;
              certificates.push(imageUrl);
              this.setData({
                'formData.qualificationCertificates': certificates
              });
            } else {
              this.setData({
                [`formData.${field}`]: imageUrl
              });
            }
            
            wx.showToast({
              title: '上传成功',
              icon: 'success'
            });
          } else {
            throw new Error(data.message || '上传失败');
          }
        } catch (e) {

          wx.showToast({
            title: '上传失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();

        wx.showToast({
          title: '上传失败',
          icon: 'none'
        });
      }
    });
  },

  // 验证表单
  validateForm: function() {
    const { formData } = this.data;

    if (!formData.name.trim()) {
      wx.showToast({ title: '请输入姓名', icon: 'none' });
      return false;
    }

    if (!formData.phone.trim()) {
      wx.showToast({ title: '请输入联系电话', icon: 'none' });
      return false;
    }

    if (!/^1[3-9]\d{9}$/.test(formData.phone)) {
      wx.showToast({ title: '请输入正确的手机号', icon: 'none' });
      return false;
    }

    if (!formData.idCard.trim()) {
      wx.showToast({ title: '请输入身份证号', icon: 'none' });
      return false;
    }

    if (!/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/.test(formData.idCard)) {
      wx.showToast({ title: '请输入正确的身份证号', icon: 'none' });
      return false;
    }

    if (!formData.experienceYears.trim()) {
      wx.showToast({ title: '请输入工作年限', icon: 'none' });
      return false;
    }

    // 检查专业技能
    const selectedSpecialties = this.data.specialtyOptions.filter(item => item.checked);
    if (selectedSpecialties.length === 0) {
      wx.showToast({ title: '请选择至少一项专业技能', icon: 'none' });
      return false;
    }

    if (!formData.idCardFront) {
      wx.showToast({ title: '请上传身份证正面', icon: 'none' });
      return false;
    }

    if (!formData.idCardBack) {
      wx.showToast({ title: '请上传身份证背面', icon: 'none' });
      return false;
    }

    return true;
  },

  // 提交申请
  submitApplication: function() {
    if (!this.validateForm()) {
      return;
    }

    this.setData({ submitting: true });

    // 准备提交数据
    const submitData = {
      ...this.data.formData,
      specialties: this.data.specialtyOptions.filter(item => item.checked).map(item => item.name),
      workTime: this.data.workTimeOptions.filter(item => item.checked).map(item => item.name)
    };

    api.submitEngineerApplication(submitData).then(res => {
      this.setData({ submitting: false });

      if (res.success) {
        wx.showModal({
          title: '申请提交成功',
          content: '您的申请已提交，我们将在3-5个工作日内完成审核，请保持电话畅通。',
          showCancel: false,
          success: () => {
            wx.navigateBack();
          }
        });
      } else {
        wx.showToast({
          title: res.message || '提交失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      this.setData({ submitting: false });

      wx.showToast({
        title: '提交失败，请重试',
        icon: 'none'
      });
    });
  },

  // 页面分享
  onShareAppMessage: function () {
    return {
      title: '桩郎中工程师入驻申请',
      path: '/pages/engineer_apply/engineer_apply',
      imageUrl: '/images/share/engineer_apply.png'
    };
  },

  // 页面分享到朋友圈
  onShareTimeline: function () {
    return {
      title: '桩郎中工程师入驻申请',
      imageUrl: '/images/share/engineer_apply.png'
    };
  }
})
