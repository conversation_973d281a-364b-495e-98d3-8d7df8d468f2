<view class="container">
  <!-- 页面标题 -->
  <view class="page-title">完善个人信息</view>
  
  <!-- 头像选择 -->
  <view class="form-section">
    <view class="section-title">头像 <text class="required">*</text></view>
    <view class="avatar-section">
      <button class="avatar-wrapper" open-type="chooseAvatar" bind:chooseavatar="onChooseAvatar">
        <image src="{{avatarUrl || userInfo.avatarUrl || '/images/default-avatar.png'}}" class="avatar-image" mode="aspectFill" />
        <view class="avatar-overlay">
          <text class="avatar-text">{{avatarUrl ? '重新选择' : '点击选择头像'}}</text>
        </view>
      </button>
    </view>
  </view>

  <!-- 昵称输入 -->
  <view class="form-section">
    <view class="section-title">昵称 <text class="required">*</text></view>
    <input 
      class="input-field"
      type="nickname" 
      placeholder="请输入您的昵称"
      value="{{nickname}}"
      bindinput="onNicknameInput"
      maxlength="20" />
  </view>

  <!-- 性别选择 -->
  <view class="form-section">
    <view class="section-title">性别</view>
    <view class="gender-options">
      <view class="gender-item {{gender === '1' ? 'selected' : ''}}" bindtap="selectGender" data-gender="1">
        <text class="gender-text">男</text>
      </view>
      <view class="gender-item {{gender === '2' ? 'selected' : ''}}" bindtap="selectGender" data-gender="2">
        <text class="gender-text">女</text>
      </view>
      <view class="gender-item {{gender === '0' ? 'selected' : ''}}" bindtap="selectGender" data-gender="0">
        <text class="gender-text">保密</text>
      </view>
    </view>
  </view>

  <!-- 手机号码 -->
  <view class="form-section">
    <view class="section-title">手机号码</view>
    <input 
      class="input-field"
      type="number" 
      placeholder="请输入手机号码"
      value="{{phone}}"
      bindinput="onPhoneInput"
      maxlength="11" />
  </view>

  <!-- 地区选择 -->
  <view class="form-section">
    <view class="section-title">所在地区</view>
    <picker mode="region" bindchange="onRegionChange" value="{{region}}">
      <view class="picker-field">
        <text class="picker-text">{{regionText || '请选择所在地区'}}</text>
        <text class="picker-arrow">></text>
      </view>
    </picker>
  </view>

  <!-- 提交按钮 -->
  <view class="button-section">
    <button class="submit-btn" bindtap="submitProfile" disabled="{{!canSubmit}}">
      {{isUpdating ? '保存中...' : '保存信息'}}
    </button>
  </view>

  <!-- 提示信息 -->
  <view class="tips">
    <text class="tips-text">* 为必填项，完善信息后可享受更好的服务体验</text>
  </view>
</view>
