const app = getApp()
const auth = require('../../utils/auth.js')
const api = require('../../utils/api.js')

Page({
  data: {
    userInfo: null,
    nickname: '',
    gender: '0', // 0-保密, 1-男, 2-女
    phone: '',
    region: [],
    regionText: '',
    avatarUrl: '',
    tempAvatarUrl: '', // 临时头像URL
    isUpdating: false,
    canSubmit: false
  },

  onLoad: function(options) {
    // 获取当前用户信息
    this.loadUserInfo();
  },

  // 加载用户信息
  loadUserInfo: function() {
    const loginState = auth.getLoginState();
    
    if (loginState.isLoggedIn && loginState.userInfo) {
      const userInfo = loginState.userInfo;
      this.setData({
        userInfo: userInfo,
        nickname: userInfo.nickName || '',
        gender: userInfo.gender || '0',
        phone: userInfo.phone || '',
        avatarUrl: userInfo.avatarUrl || '',
        regionText: this.formatRegion(userInfo.province, userInfo.city)
      });
      
      this.checkCanSubmit();
    } else {
      // 未登录，返回登录页面
      wx.showModal({
        title: '提示',
        content: '请先登录',
        showCancel: false,
        success: () => {
          wx.navigateBack();
        }
      });
    }
  },

  // 选择头像
  onChooseAvatar: function(e) {
    const { avatarUrl } = e.detail;

    
    wx.showLoading({
      title: '上传头像中...'
    });

    this.uploadAvatar(avatarUrl);
  },

  // 上传头像到后端
  uploadAvatar: function(tempFilePath) {
    const uploadUrl = 'https://www.zhuanglz.cn:8443/api/upload/image';
    
    wx.uploadFile({
      url: uploadUrl,
      filePath: tempFilePath,
      name: 'file',
      header: {
        'Content-Type': 'multipart/form-data'
      },
      success: (res) => {
        wx.hideLoading();
        
        try {
          const data = JSON.parse(res.data);
          if (data.success && data.data && data.data.url) {
            const serverPath = data.data.url; // 服务器路径，如 "/uploads/xxx.jpg"
            const fullUrl = `https://www.zhuanglz.cn:8443${serverPath}`; // 完整URL用于显示
            this.setData({
              tempAvatarUrl: serverPath,  // 保存服务器路径，用于提交
              avatarUrl: fullUrl,         // 保存完整URL，用于显示
              'userInfo.avatarUrl': fullUrl  // 同时更新userInfo中的头像
            });

            this.checkCanSubmit();

            wx.showToast({
              title: '头像上传成功',
              icon: 'success'
            });
          } else {

            wx.showToast({
              title: data.message || '头像上传失败',
              icon: 'none'
            });
          }
        } catch (err) {

          wx.showToast({
            title: '头像上传失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();

        wx.showToast({
          title: '头像上传失败，请重试',
          icon: 'none'
        });
      }
    });
  },

  // 昵称输入
  onNicknameInput: function(e) {
    this.setData({
      nickname: e.detail.value
    });
    this.checkCanSubmit();
  },

  // 选择性别
  selectGender: function(e) {
    const gender = e.currentTarget.dataset.gender;
    this.setData({
      gender: gender
    });
  },

  // 手机号输入
  onPhoneInput: function(e) {
    this.setData({
      phone: e.detail.value
    });
  },

  // 地区选择
  onRegionChange: function(e) {
    const region = e.detail.value;
    this.setData({
      region: region,
      regionText: region.join(' ')
    });
  },

  // 检查是否可以提交
  checkCanSubmit: function() {
    const { nickname, avatarUrl, tempAvatarUrl } = this.data;
    const canSubmit = nickname.trim().length > 0 && (avatarUrl || tempAvatarUrl);
    
    this.setData({
      canSubmit: canSubmit
    });
  },

  // 提交个人信息
  submitProfile: function() {
    if (!this.data.canSubmit || this.data.isUpdating) {
      return;
    }

    const { nickname, gender, phone, region, tempAvatarUrl, avatarUrl } = this.data;
    
    // 验证必填项
    if (!nickname.trim()) {
      wx.showToast({
        title: '请输入昵称',
        icon: 'none'
      });
      return;
    }

    if (!avatarUrl && !tempAvatarUrl) {
      wx.showToast({
        title: '请选择头像',
        icon: 'none'
      });
      return;
    }

    this.setData({
      isUpdating: true
    });

    // 构建用户信息
    const updatedUserInfo = {
      nickName: nickname.trim(),
      gender: gender,
      phone: phone.trim(),
      avatarUrl: tempAvatarUrl || avatarUrl
    };

    // 添加地区信息
    if (region.length >= 2) {
      updatedUserInfo.province = region[0];
      updatedUserInfo.city = region[1];
    }

    const openId = this.data.userInfo.openId || app.globalData.openId;

    api.updateUserInfo(openId, updatedUserInfo).then(res => {
      this.setData({
        isUpdating: false
      });

      if (res.success) {
        // 更新本地页面数据
        this.setData({
          userInfo: res.userInfo,
          nickname: res.userInfo.nickName || '',
          gender: res.userInfo.gender || '0',
          phone: res.userInfo.phone || '',
          avatarUrl: res.userInfo.avatarUrl || '',
          tempAvatarUrl: '' // 清空临时头像URL
        });

        // 更新本地认证状态
        auth.saveLoginState(
          openId,
          res.userInfo,
          this.data.userInfo.sessionToken || app.globalData.sessionToken
        );

        wx.showToast({
          title: '保存成功',
          icon: 'success'
        });

        // 延迟返回上一页
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      } else {

        wx.showToast({
          title: res.message || '保存失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      this.setData({
        isUpdating: false
      });
      wx.showToast({
        title: '保存失败，请重试',
        icon: 'none'
      });
    });
  },

  // 格式化地区显示
  formatRegion: function(province, city) {
    if (province && city) {
      return `${province} ${city}`;
    }
    return '';
  }
});
