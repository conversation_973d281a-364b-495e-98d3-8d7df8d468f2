.container {
  padding: 40rpx 30rpx;
  background-color: #f8f9fa;
  min-height: 100vh;
}

.page-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  text-align: center;
  margin-bottom: 60rpx;
}

.form-section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.required {
  color: #ff4757;
  margin-left: 4rpx;
}

/* 头像选择 */
.avatar-section {
  display: flex;
  justify-content: center;
  align-items: center;
}

.avatar-wrapper {
  position: relative;
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  overflow: hidden;
  background: none;
  border: none;
  padding: 0;
}

.avatar-image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

.avatar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.3s;
}

.avatar-wrapper:active .avatar-overlay {
  opacity: 1;
}

.avatar-text {
  color: white;
  font-size: 24rpx;
  text-align: center;
}

/* 输入框 */
.input-field {
  width: 100%;
  height: 80rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
  border: 2rpx solid transparent;
  transition: border-color 0.3s;
}

.input-field:focus {
  border-color: #1e88e5;
  background: white;
}

/* 性别选择 */
.gender-options {
  display: flex;
  gap: 20rpx;
}

.gender-item {
  flex: 1;
  height: 80rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx solid transparent;
  transition: all 0.3s;
}

.gender-item.selected {
  background: #e3f2fd;
  border-color: #1e88e5;
}

.gender-text {
  font-size: 28rpx;
  color: #333;
}

.gender-item.selected .gender-text {
  color: #1e88e5;
  font-weight: 600;
}

/* 选择器 */
.picker-field {
  height: 80rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 0 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border: 2rpx solid transparent;
  transition: border-color 0.3s;
}

.picker-field:active {
  border-color: #1e88e5;
  background: white;
}

.picker-text {
  font-size: 28rpx;
  color: #333;
}

.picker-arrow {
  font-size: 24rpx;
  color: #999;
}

/* 按钮 */
.button-section {
  margin-top: 60rpx;
  margin-bottom: 40rpx;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #1e88e5, #1976d2);
  color: white;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 600;
  border: none;
  box-shadow: 0 8rpx 24rpx rgba(30, 136, 229, 0.3);
  transition: all 0.3s;
}

.submit-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(30, 136, 229, 0.3);
}

.submit-btn[disabled] {
  background: #ccc;
  box-shadow: none;
  transform: none;
}

/* 提示信息 */
.tips {
  text-align: center;
  padding: 0 20rpx;
}

.tips-text {
  font-size: 24rpx;
  color: #999;
  line-height: 1.5;
}
