<view class="container">
  <!-- 步骤指示器 -->
  <view class="steps-container">
    <scroll-view scroll-x="true" class="steps" show-scrollbar="false">
      <view class="step active">
        <view class="step-number">1</view>
        <view class="step-label">填写信息</view>
      </view>
      <view class="step active">
        <view class="step-number">2</view>
        <view class="step-label">选择时间</view>
      </view>
      <view class="step">
        <view class="step-number">3</view>
        <view class="step-label">提交订单</view>
      </view>
      <view class="step">
        <view class="step-number">4</view>
        <view class="step-label">等待上门</view>
      </view>
    </scroll-view>
  </view>

  <!-- 日期选择 -->
  <view class="card">
    <view class="section-title">选择预约日期</view>
    
    <!-- 月份标题 -->
    <view class="month-header">
      <text>{{year}}年{{month + 1}}月</text>
    </view>
    
    <!-- 星期标题 -->
    <view class="weekdays">
      <view class="weekday" wx:for="{{weekdays}}" wx:key="*this">{{item}}</view>
    </view>
    
    <!-- 日期网格 -->
    <view class="calendar">
      <view 
        class="day {{item.disabled ? 'disabled' : ''}} {{item.selected ? 'selected' : ''}} {{item.isToday ? 'today' : ''}}"
        wx:for="{{days}}" 
        wx:key="index"
        bindtap="onDateTap"
        data-date="{{item.date}}"
        data-disabled="{{item.disabled}}"
      >
        <text wx:if="{{!item.isOtherMonth}}">{{item.date}}</text>
      </view>
    </view>
  </view>

  <!-- 时间段选择 -->
  <view class="card">
    <view class="section-title">选择预约时间</view>
    
    <view class="time-slots">
      <view 
        class="time-slot {{item.disabled ? 'disabled' : ''}} {{item.selected ? 'selected' : ''}}"
        wx:for="{{timeSlots}}" 
        wx:key="index"
        bindtap="onTimeSlotTap"
        data-index="{{index}}"
      >
        <text>{{item.time}}</text>
        <text class="slot-status" wx:if="{{item.disabled}}">已满</text>
      </view>
    </view>
    
    <view class="time-tips">
      <text class="tip-text">• 建议提前1天预约，确保技师及时安排</text>
      <text class="tip-text">• 如需紧急维修，请直接拨打客服电话</text>
    </view>
  </view>

  <!-- 预约信息确认 -->
  <view class="card" wx:if="{{selectedDate && selectedTimeStr}}">
    <view class="section-title">预约信息确认</view>
    
    <view class="confirm-info">
      <view class="info-row">
        <text class="info-label">预约日期：</text>
        <text class="info-value">{{selectedDateStr}}</text>
      </view>
      <view class="info-row">
        <text class="info-label">预约时间：</text>
        <text class="info-value">{{selectedTimeStr}}</text>
      </view>
    </view>
  </view>

  <!-- 底部按钮 -->
  <view class="bottom-actions">
    <button class="btn-primary" bindtap="submitAppointment">确认预约</button>
  </view>
</view>
