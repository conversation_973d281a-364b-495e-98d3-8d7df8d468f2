.container {
  padding: 30rpx;
  padding-bottom: calc(160rpx + env(safe-area-inset-bottom));
  background-color: #f5f5f5;
  min-height: 100vh;
}

.steps-container {
  background-color: white;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.steps {
  white-space: nowrap;
}

.step {
  display: inline-block;
  text-align: center;
  margin-right: 60rpx;
  position: relative;
}

.step:last-child {
  margin-right: 0;
}

.step:not(:last-child)::after {
  content: '';
  position: absolute;
  top: 20rpx;
  right: -30rpx;
  width: 60rpx;
  height: 2rpx;
  background-color: #e0e0e0;
}

.step.active:not(:last-child)::after {
  background-color: #1e88e5;
}

.step-number {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background-color: #e0e0e0;
  color: #999;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 24rpx;
  margin: 0 auto 8rpx;
}

.step.active .step-number {
  background-color: #1e88e5;
  color: white;
}

.step-label {
  font-size: 24rpx;
  color: #666;
}

.step.active .step-label {
  color: #1e88e5;
  font-weight: 500;
}

.card {
  background-color: white;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
}

.month-header {
  text-align: center;
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
}

.weekdays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 8rpx;
  margin-bottom: 16rpx;
}

.weekday {
  text-align: center;
  font-size: 28rpx;
  color: #666;
  padding: 16rpx 0;
}

.calendar {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 8rpx;
}

.day {
  aspect-ratio: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #333;
  cursor: pointer;
}

.day.disabled {
  color: #ccc;
  cursor: not-allowed;
}

.day.today {
  background-color: #e3f2fd;
  color: #1e88e5;
  font-weight: 600;
}

.day.selected {
  background-color: #1e88e5;
  color: white;
  font-weight: 600;
}

.day:not(.disabled):not(.selected):hover {
  background-color: #f5f5f5;
}

.time-slots {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
  margin-bottom: 32rpx;
}

.time-slot {
  padding: 24rpx;
  border: 2px solid #e0e0e0;
  border-radius: 16rpx;
  text-align: center;
  background-color: #f9f9f9;
  position: relative;
}

.time-slot.selected {
  border-color: #1e88e5;
  background-color: #e3f2fd;
  color: #1e88e5;
  font-weight: 500;
}

.time-slot.disabled {
  background-color: #f0f0f0;
  color: #ccc;
  cursor: not-allowed;
}

.slot-status {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  font-size: 20rpx;
  color: #f44336;
}

.time-tips {
  padding: 24rpx;
  background-color: #f9f9f9;
  border-radius: 16rpx;
  border-left: 4rpx solid #1e88e5;
}

.tip-text {
  display: block;
  font-size: 24rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 8rpx;
}

.tip-text:last-child {
  margin-bottom: 0;
}

.confirm-info {
  background-color: #f9f9f9;
  border-radius: 16rpx;
  padding: 24rpx;
}

.info-row {
  display: flex;
  margin-bottom: 16rpx;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 28rpx;
  color: #666;
  min-width: 160rpx;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  padding: 30rpx;
  padding-bottom: calc(30rpx + env(safe-area-inset-bottom));
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
}

.btn-primary {
  width: 100%;
  height: 88rpx;
  background-color: #1e88e5;
  color: white;
  border: none;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(30, 136, 229, 0.3);
}

.btn-primary::after {
  border: none;
}
