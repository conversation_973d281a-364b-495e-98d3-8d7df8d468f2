const app = getApp()

Page({
  data: {
    weekdays: ['日', '一', '二', '三', '四', '五', '六'],
    year: 0,
    month: 0,
    days: [],
    selectedDate: null,
    selectedDateStr: '',
    timeSlots: [
      { time: '08:00-09:00', disabled: true },
      { time: '09:00-10:00', disabled: true },
      { time: '10:00-11:00', disabled: false, selected: false },
      { time: '11:00-12:00', disabled: false, selected: false },
      { time: '13:00-14:00', disabled: false, selected: false },
      { time: '14:00-15:00', disabled: false, selected: true },
      { time: '15:00-16:00', disabled: false, selected: false },
      { time: '16:00-17:00', disabled: false, selected: false },
      { time: '17:00-18:00', disabled: false, selected: false }
    ],
    selectedTimeIndex: 3, // 默认选中14:00-15:00
    selectedTimeStr: '14:00-15:00',
    openId: '' // 用户openId
  },

  onLoad: function () {
    console.log('维修时间选择页面加载');
    
    // 初始化日历
    this.initCalendar();
    
    // 获取用户openId
    this.getUserOpenId();
  },

  // 获取用户openId
  getUserOpenId: function() {
    const auth = require('../../utils/auth');
    const loginState = auth.getLoginState();
    
    if (loginState.isLoggedIn && loginState.openId) {
      this.setData({
        openId: loginState.openId
      });
    }
  },

  // 初始化日历
  initCalendar: function() {
    const now = new Date();
    const year = now.getFullYear();
    const month = now.getMonth();
    
    this.setData({
      year: year,
      month: month
    });
    
    this.generateCalendar(year, month);
    
    // 默认选择明天
    const tomorrow = new Date(now.getTime() + 24 * 60 * 60 * 1000);
    this.selectDate(tomorrow.getDate());
  },

  // 生成日历
  generateCalendar: function(year, month) {
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const firstDayWeek = firstDay.getDay();
    const daysInMonth = lastDay.getDate();
    
    const days = [];
    
    // 添加上个月的日期（占位）
    for (let i = 0; i < firstDayWeek; i++) {
      days.push({ date: '', disabled: true, isOtherMonth: true });
    }
    
    // 添加当月的日期
    const today = new Date();
    for (let i = 1; i <= daysInMonth; i++) {
      const currentDate = new Date(year, month, i);
      const isToday = currentDate.toDateString() === today.toDateString();
      const isPast = currentDate < today && !isToday;
      
      days.push({
        date: i,
        disabled: isPast,
        isToday: isToday,
        isOtherMonth: false,
        selected: false
      });
    }
    
    this.setData({ days });
  },

  // 选择日期
  selectDate: function(date) {
    if (!date) return;
    
    const days = this.data.days.map(day => ({
      ...day,
      selected: day.date === date && !day.disabled && !day.isOtherMonth
    }));
    
    const selectedDay = days.find(day => day.selected);
    if (selectedDay) {
      const selectedDate = new Date(this.data.year, this.data.month, date);
      const selectedDateStr = this.formatDate(selectedDate);
      
      this.setData({
        days: days,
        selectedDate: selectedDate,
        selectedDateStr: selectedDateStr
      });
    }
  },

  // 日期点击事件
  onDateTap: function(e) {
    const date = e.currentTarget.dataset.date;
    const disabled = e.currentTarget.dataset.disabled;
    
    if (!disabled && date) {
      this.selectDate(date);
    }
  },

  // 时间段选择
  onTimeSlotTap: function(e) {
    const index = e.currentTarget.dataset.index;
    const timeSlot = this.data.timeSlots[index];
    
    if (timeSlot.disabled) return;
    
    const timeSlots = this.data.timeSlots.map((slot, i) => ({
      ...slot,
      selected: i === index
    }));
    
    this.setData({
      timeSlots: timeSlots,
      selectedTimeIndex: index,
      selectedTimeStr: timeSlot.time
    });
  },

  // 格式化日期
  formatDate: function(date) {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  },

  // 上传图片
  uploadImages: function(images) {
    if (!images || images.length === 0) {
      return Promise.resolve([]);
    }

    const api = require('../../utils/api');
    const uploadPromises = images.map(imagePath => api.uploadImage(imagePath));
    
    return Promise.all(uploadPromises).then(results => {
      return results.map(result => {
        if (result.success) {
          return result.data.url;
        } else {
          console.error('图片上传失败:', result);
          return null;
        }
      }).filter(url => url !== null);
    });
  },

  // 提交预约
  submitAppointment: function() {
    // 检查是否选择了日期和时间
    if (!this.data.selectedDate) {
      wx.showToast({
        title: '请选择预约日期',
        icon: 'none'
      });
      return;
    }
    
    if (this.data.selectedTimeIndex === -1) {
      wx.showToast({
        title: '请选择预约时间段',
        icon: 'none'
      });
      return;
    }
    
    // 检查是否有openId
    if (!this.data.openId) {
      wx.showModal({
        title: '请先登录',
        content: '需要登录后才能提交维修申请',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            app.login && app.login();
          }
        }
      });
      return;
    }
    
    // 获取维修信息
    const repairInfo = app.globalData.repairInfo || {};
    
    // 添加预约时间
    repairInfo.appointmentDate = this.data.selectedDateStr;
    repairInfo.appointmentTime = this.data.selectedTimeStr;
    repairInfo.openId = this.data.openId;
    
    // 显示加载中
    wx.showLoading({
      title: '提交预约中'
    });

    // 先上传图片，再创建订单
    this.uploadImages(repairInfo.images)
      .then(uploadedImageUrls => {
        // 调用API创建维修订单
        const api = require('../../utils/api');

        // 构建维修订单数据
        const repairOrderData = {
          openId: this.data.openId,
          faultType: repairInfo.faultType,
          model: repairInfo.model,
          description: repairInfo.description,
          name: repairInfo.name,
          phone: repairInfo.phone,
          addressId: repairInfo.addressId,
          fullAddress: repairInfo.fullAddress,
          serviceType: repairInfo.serviceType,
          images: uploadedImageUrls.length > 0 ? JSON.stringify(uploadedImageUrls) : null,
          appointmentDate: repairInfo.appointmentDate,
          appointmentTime: repairInfo.appointmentTime
        };

        return api.submitRepairRequest(repairOrderData);
      })
      .then(result => {
        wx.hideLoading();
        
        if (result.success) {
          // 保存订单信息到全局数据
          app.globalData.repairOrderId = result.order ? result.order.id : null;
          app.globalData.repairOrderNo = result.orderNo;
          app.globalData.repairOrder = result.order;

          // 跳转到成功页面
          wx.redirectTo({
            url: '/pages/repair_success/repair_success'
          });
        } else {
          wx.showToast({
            title: result.message || '提交失败，请重试',
            icon: 'none'
          });
        }
      })
      .catch(err => {
        wx.hideLoading();
        console.error('提交维修申请失败:', err);
        wx.showToast({
          title: '提交失败，请重试',
          icon: 'none'
        });
      });
  }
})
