const app = getApp()

Page({
  data: {
    cartItems: [],
    totalPrice: 0,
    selectedItems: [],
    selectAll: false,
    isEmpty: true
  },

  onLoad: function () {
    this.loadCartItems();
  },

  onShow: function () {
    this.loadCartItems();
  },

  // 加载购物车数据
  loadCartItems: function() {
    const cartItems = app.globalData.cartItems || [];
    
    this.setData({
      cartItems: cartItems,
      isEmpty: cartItems.length === 0,
      selectedItems: cartItems.map(item => item.id)
    });
    
    this.calculateTotal();
  },

  // 计算总价
  calculateTotal: function() {
    const { cartItems, selectedItems } = this.data;
    let totalPrice = 0;
    
    cartItems.forEach(item => {
      if (selectedItems.includes(item.id)) {
        totalPrice += item.price * item.quantity;
      }
    });
    
    this.setData({
      totalPrice: totalPrice.toFixed(2),
      selectAll: selectedItems.length === cartItems.length && cartItems.length > 0
    });
  },

  // 选择商品
  onItemSelect: function(e) {
    const itemId = e.currentTarget.dataset.id;
    let selectedItems = [...this.data.selectedItems];
    
    if (selectedItems.includes(itemId)) {
      selectedItems = selectedItems.filter(id => id !== itemId);
    } else {
      selectedItems.push(itemId);
    }
    
    this.setData({ selectedItems });
    this.calculateTotal();
  },

  // 全选/取消全选
  onSelectAll: function() {
    const { selectAll, cartItems } = this.data;
    
    if (selectAll) {
      this.setData({ selectedItems: [] });
    } else {
      this.setData({ selectedItems: cartItems.map(item => item.id) });
    }
    
    this.calculateTotal();
  },

  // 数量变更
  onQuantityChange: function(e) {
    const { id, type } = e.currentTarget.dataset;
    const cartItems = [...this.data.cartItems];
    
    const itemIndex = cartItems.findIndex(item => item.id === id);
    if (itemIndex === -1) return;
    
    if (type === 'minus' && cartItems[itemIndex].quantity > 1) {
      cartItems[itemIndex].quantity--;
    } else if (type === 'plus' && cartItems[itemIndex].quantity < 99) {
      cartItems[itemIndex].quantity++;
    }
    
    // 更新全局数据
    app.globalData.cartItems = cartItems;
    
    this.setData({ cartItems });
    this.calculateTotal();
  },

  // 删除商品
  onDeleteItem: function(e) {
    const itemId = e.currentTarget.dataset.id;
    
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这个商品吗？',
      success: (res) => {
        if (res.confirm) {
          app.removeFromCart(itemId);
          this.loadCartItems();
          
          wx.showToast({
            title: '已删除',
            icon: 'success'
          });
        }
      }
    });
  },

  // 清空购物车
  onClearCart: function() {
    if (this.data.cartItems.length === 0) return;
    
    wx.showModal({
      title: '确认清空',
      content: '确定要清空购物车吗？',
      success: (res) => {
        if (res.confirm) {
          app.globalData.cartItems = [];
          this.loadCartItems();
          
          wx.showToast({
            title: '已清空购物车',
            icon: 'success'
          });
        }
      }
    });
  },

  // 去结算
  onCheckout: function() {
    const { selectedItems, cartItems } = this.data;
    
    if (selectedItems.length === 0) {
      wx.showToast({
        title: '请选择要结算的商品',
        icon: 'none'
      });
      return;
    }
    
    const selectedProducts = cartItems.filter(item => 
      selectedItems.includes(item.id)
    );
    
    // 跳转到订单确认页面
    wx.navigateTo({
      url: '/pages/order_confirm/order_confirm?from=cart&products=' + 
           encodeURIComponent(JSON.stringify(selectedProducts))
    });
  },

  // 跳转到商品详情
  goToProductDetail: function(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/product_detail/product_detail?id=${id}`
    });
  },

  // 去购物
  goToMall: function() {
    wx.switchTab({
      url: '/pages/mall/mall'
    });
  }
})
