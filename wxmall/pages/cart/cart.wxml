<view class="container">
  <!-- 购物车列表 -->
  <view class="cart-list" wx:if="{{!isEmpty}}">
    <view class="cart-item" wx:for="{{cartItems}}" wx:key="id">
      <view class="item-select">
        <view 
          class="checkbox {{selectedItems.includes(item.id) ? 'checked' : ''}}"
          bindtap="onItemSelect"
          data-id="{{item.id}}"
        >
          <text wx:if="{{selectedItems.includes(item.id)}}">✓</text>
        </view>
      </view>
      
      <view class="item-image" bindtap="goToProductDetail" data-id="{{item.id}}">
        <image src="{{item.image}}" mode="aspectFill" />
      </view>
      
      <view class="item-info">
        <view class="item-name" bindtap="goToProductDetail" data-id="{{item.id}}">
          {{item.name}}
        </view>
        <view class="item-price">¥{{item.price}}</view>
        
        <view class="item-actions">
          <view class="quantity-selector">
            <button 
              class="quantity-btn" 
              bindtap="onQuantityChange" 
              data-id="{{item.id}}" 
              data-type="minus"
            >-</button>
            <input class="quantity-input" type="number" value="{{item.quantity}}" disabled />
            <button 
              class="quantity-btn" 
              bindtap="onQuantityChange" 
              data-id="{{item.id}}" 
              data-type="plus"
            >+</button>
          </view>
          
          <button 
            class="delete-btn" 
            bindtap="onDeleteItem" 
            data-id="{{item.id}}"
          >删除</button>
        </view>
      </view>
    </view>
  </view>

  <!-- 空购物车状态 -->
  <view class="empty-cart" wx:if="{{isEmpty}}">
    <view class="empty-icon">🛒</view>
    <view class="empty-title">购物车是空的</view>
    <view class="empty-description">快去挑选你喜欢的商品吧</view>
    <button class="go-shopping-btn" bindtap="goToMall">去购物</button>
  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-actions" wx:if="{{!isEmpty}}">
    <view class="select-all">
      <view 
        class="checkbox {{selectAll ? 'checked' : ''}}"
        bindtap="onSelectAll"
      >
        <text wx:if="{{selectAll}}">✓</text>
      </view>
      <text>全选</text>
    </view>
    
    <view class="total-info">
      <text class="total-label">合计：</text>
      <text class="total-price">¥{{totalPrice}}</text>
    </view>
    
    <view class="action-buttons">
      <button class="clear-btn" bindtap="onClearCart">清空</button>
      <button class="checkout-btn" bindtap="onCheckout">
        结算({{selectedItems.length}})
      </button>
    </view>
  </view>
</view>
