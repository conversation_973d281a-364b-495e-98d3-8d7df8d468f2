.container {
  padding-bottom: 120rpx;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.cart-list {
  background-color: white;
  margin: 20rpx;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.cart-item {
  display: flex;
  padding: 32rpx;
  border-bottom: 1px solid #f0f0f0;
  align-items: flex-start;
}

.cart-item:last-child {
  border-bottom: none;
}

.item-select {
  margin-right: 24rpx;
  padding-top: 20rpx;
}

.checkbox {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  border: 2px solid #e0e0e0;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 24rpx;
  color: white;
}

.checkbox.checked {
  background-color: #1e88e5;
  border-color: #1e88e5;
}

.item-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 16rpx;
  overflow: hidden;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.item-image image {
  width: 100%;
  height: 100%;
}

.item-info {
  flex: 1;
}

.item-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 16rpx;
  line-height: 1.4;
}

.item-price {
  font-size: 36rpx;
  font-weight: 600;
  color: #f44336;
  margin-bottom: 24rpx;
}

.item-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.quantity-selector {
  display: flex;
  align-items: center;
  border: 1px solid #e0e0e0;
  border-radius: 8rpx;
  overflow: hidden;
}

.quantity-btn {
  width: 60rpx;
  height: 60rpx;
  background-color: #f5f5f5;
  border: none;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32rpx;
  color: #333;
}

.quantity-input {
  width: 100rpx;
  height: 60rpx;
  text-align: center;
  border: none;
  background-color: white;
  font-size: 28rpx;
}

.delete-btn {
  background-color: transparent;
  border: 1px solid #f44336;
  color: #f44336;
  border-radius: 16rpx;
  padding: 12rpx 24rpx;
  font-size: 24rpx;
}

.empty-cart {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
}

.empty-title {
  font-size: 36rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 16rpx;
}

.empty-description {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 48rpx;
}

.go-shopping-btn {
  background-color: #1e88e5;
  color: white;
  border: none;
  border-radius: 32rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  padding: 24rpx 32rpx;
  border-top: 1px solid #e0e0e0;
  display: flex;
  align-items: center;
  z-index: 100;
}

.select-all {
  display: flex;
  align-items: center;
  gap: 16rpx;
  font-size: 28rpx;
  color: #333;
}

.total-info {
  flex: 1;
  text-align: center;
  font-size: 28rpx;
}

.total-label {
  color: #666;
}

.total-price {
  color: #f44336;
  font-size: 36rpx;
  font-weight: 600;
}

.action-buttons {
  display: flex;
  gap: 16rpx;
}

.clear-btn {
  background-color: #f5f5f5;
  color: #666;
  border: none;
  border-radius: 24rpx;
  padding: 20rpx 32rpx;
  font-size: 28rpx;
}

.checkout-btn {
  background-color: #f44336;
  color: white;
  border: none;
  border-radius: 24rpx;
  padding: 20rpx 32rpx;
  font-size: 28rpx;
}
