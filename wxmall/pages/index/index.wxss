/* 首页样式 - 参考抖音小程序设计 */
.container {
  padding: 30rpx 30rpx 120rpx;
  background-color: #f5f5f5;
}

.section {
  margin-bottom: 40rpx;
}

/* 轮播图 */
.banner {
  width: 100%;
  height: 300rpx;
  border-radius: 24rpx;
  overflow: hidden;
  margin-bottom: 32rpx;
}

.banner image {
  width: 100%;
  height: 100%;
}

/* 分类网格 */
.category-grid {
  display: flex;
  justify-content: space-between;
  margin-bottom: 40rpx;
  padding: 0 10rpx;
  flex-wrap: wrap;
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  max-width: 20%;
  min-width: 18%;
}

.category-icon {
  width: 100rpx;
  height: 100rpx;
  background-color: #e3f2fd;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 16rpx;
  color: #1e88e5;
  font-size: 40rpx;
}

.category-name {
  font-size: 24rpx;
  color: #333;
  text-align: center;
  line-height: 1.2;
}

/* 段落标题 */
.section-title {
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 24rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-title .more {
  font-size: 28rpx;
  color: #757575;
  font-weight: normal;
  display: flex;
  align-items: center;
}

.arrow-icon {
  font-size: 24rpx;
  color: #757575;
  margin-left: 8rpx;
}

/* 网格布局 */
.grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
}

.grid-3 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24rpx;
}

/* 商品卡片 */
.product-card {
  background-color: #ffffff;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.product-card image {
  width: 100%;
  height: 240rpx;
}

.product-card .info {
  padding: 20rpx;
}

.product-card .title {
  font-size: 28rpx;
  font-weight: 500;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-card .price {
  color: #f44336;
  font-weight: 600;
  font-size: 32rpx;
}

/* 卡片组件 */
.card {
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

/* 维修项目 */
.repair-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
}

/* 工具类 */
.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.mb-2 {
  margin-bottom: 16rpx;
}

.mt-3 {
  margin-top: 24rpx;
}

.font-bold {
  font-weight: 600;
}

.text-sm {
  font-size: 28rpx;
}

.badge {
  display: inline-block;
  padding: 4rpx 16rpx;
  background-color: #1e88e5;
  color: #ffffff;
  border-radius: 8rpx;
  font-size: 24rpx;
}

.btn {
  display: inline-block;
  padding: 20rpx 40rpx;
  background-color: #1e88e5;
  color: #ffffff;
  border-radius: 16rpx;
  text-align: center;
  font-weight: 500;
  border: none;
  font-size: 28rpx;
}

.btn-block {
  display: block;
  width: 100%;
}



