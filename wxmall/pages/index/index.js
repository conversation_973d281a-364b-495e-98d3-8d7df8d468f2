const app = getApp()
const api = require('../../utils/api');
const { imageManager } = require('../../utils/imageManager');

Page({
  data: {
    banners: [], // 动态加载轮播图
    categories: [
      {
        id: 1,
        name: '故障报修',
        image: '', // 动态加载图标
        type: 'repair'
      },
      {
        id: 2,
        name: '配件产品',
        image: '', // 动态加载图标
        type: 'mall'
      },
      {
        id: 3,
        name: '服务网点',
        image: '', // 动态加载图标
        type: 'service'
      },
      {
        id: 4,
        name: '在线咨询',
        image: '', // 动态加载图标
        type: 'consult'
      },
      {
        id: 5,
        name: '合作加盟',
        image: '', // 动态加载图标
        type: 'join'
      }
    ],
    hotProducts: [],
    commonFaults: [
      {
        id: 1,
        name: '无法充电',
        image: '', // 动态加载图标
        type: 'no_charging'
      },
      {
        id: 2,
        name: '充电慢',
        image: '', // 动态加载图标
        type: 'slow_charging'
      },
      {
        id: 3,
        name: '报错代码',
        image: '', // 动态加载图标
        type: 'error_code'
      }
    ]
  },

  onLoad: function () {
    // 页面加载时执行
    this.loadImageResources();
    this.loadHotProducts();
  },

  onShow: function () {
    // 页面显示时执行

    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 0
      })
    }
  },

  // 加载图片资源
  loadImageResources: function() {


    // 加载轮播图
    this.loadBanners();

    // 加载导航图标
    this.loadNavigationIcons();

    // 加载故障图标
    this.loadFaultIcons();
  },

  // 加载轮播图
  loadBanners: function() {
    imageManager.getBanners('home').then(banners => {


      // 转换为页面需要的格式
      const formattedBanners = banners.map((banner, index) => ({
        id: banner.id || index + 1,
        image: banner.url,
        type: 'banner',
        name: banner.name
      }));

      this.setData({
        banners: formattedBanners
      });
    }).catch(error => {

      // 使用默认轮播图
      this.setData({
        banners: [
          {
            id: 1,
            image: imageManager.getDefaultImageUrl('banner'),
            type: 'banner',
            name: '默认轮播图1'
          }
        ]
      });
    });
  },

  // 加载导航图标
  loadNavigationIcons: function() {
    imageManager.getIcons().then(icons => {


      // 更新categories中的图标
      const categories = this.data.categories.map(category => {
        // 根据类型匹配图标
        const iconName = this.getIconNameByType(category.type);
        const icon = icons.find(item =>
          item.name.includes(iconName) ||
          item.name.includes(category.name)
        );

        return {
          ...category,
          image: icon ? icon.url : imageManager.getDefaultImageUrl('icon')
        };
      });

      this.setData({
        categories: categories
      });
    }).catch(error => {

      // 使用默认图标
      const categories = this.data.categories.map(category => ({
        ...category,
        image: imageManager.getDefaultImageUrl('icon')
      }));

      this.setData({
        categories: categories
      });
    });
  },

  // 加载故障图标
  loadFaultIcons: function() {
    imageManager.getIcons().then(icons => {


      // 更新commonFaults中的图标
      const commonFaults = this.data.commonFaults.map(fault => {
        // 根据类型匹配图标
        const iconName = this.getFaultIconNameByType(fault.type);
        const icon = icons.find(item =>
          item.name.includes(iconName) ||
          item.name.includes(fault.name)
        );

        return {
          ...fault,
          image: icon ? icon.url : imageManager.getDefaultImageUrl('icon')
        };
      });

      this.setData({
        commonFaults: commonFaults
      });
    }).catch(error => {

      // 使用默认图标
      const commonFaults = this.data.commonFaults.map(fault => ({
        ...fault,
        image: imageManager.getDefaultImageUrl('icon')
      }));

      this.setData({
        commonFaults: commonFaults
      });
    });
  },

  // 根据类型获取图标名称
  getIconNameByType: function(type) {
    const iconMap = {
      repair: '故障报修',
      mall: '商城',
      service: '服务网点',
      consult: '在线咨询',
      join: '合作加盟'
    };
    return iconMap[type] || type;
  },

  // 根据故障类型获取图标名称
  getFaultIconNameByType: function(type) {
    const faultIconMap = {
      no_charging: '无法充电',
      slow_charging: '充电慢',
      error_code: '错误代码'
    };
    return faultIconMap[type] || type;
  },

  // 加载热门产品
  loadHotProducts: function() {


    api.getHotProducts(2).then(res => {

      
      if (res.success && res.data && res.data.length > 0) {
        // 处理产品数据，确保图片URL正确，并限制只显示前2个
        const hotProducts = res.data.slice(0, 2).map(product => {
          return {
            id: product.id,
            name: product.name || product.shortName || '商品名称',
            price: product.price || 0,
            image: this.processImageUrl(product.mainImage)
          };
        });

        this.setData({
          hotProducts: hotProducts
        });

      } else {
        // 如果没有数据，使用默认数据
        this.setData({
          hotProducts: [
            {
              id: 1,
              name: '快充充电枪',
              price: 299.00,
              image: '/images/products/快充充电枪.png'
            },
            {
              id: 2,
              name: '5米加长型充电线缆',
              price: 199.00,
              image: '/images/products/充电线缆.png'
            }
          ]
        });
      }
    }).catch(err => {

      // 出错时使用默认数据
      this.setData({
        hotProducts: [
          {
            id: 1,
            name: '快充充电枪',
            price: 299.00,
            image: '/images/products/快充充电枪.png'
          },
          {
            id: 2,
            name: '5米加长型充电线缆',
            price: 199.00,
            image: '/images/products/充电线缆.png'
          }
        ]
      });
    });
  },

  // 处理图片URL
  processImageUrl: function(imageUrl) {
    if (!imageUrl || imageUrl.trim() === '') {
      return '/images/products/充电线缆.png'; // 默认图片
    }

    // 如果已经是完整的HTTP/HTTPS URL，直接返回
    if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
      return imageUrl;
    }

    // 如果是相对路径，添加服务器地址
    if (imageUrl.startsWith('/uploads/')) {
      return `https://www.zhuanglz.cn:8443${imageUrl}`;
    }

    // 如果只是文件名，添加完整路径
    if (!imageUrl.startsWith('/')) {
      return `https://www.zhuanglz.cn:8443/uploads/${imageUrl}`;
    }

    // 其他情况，添加服务器地址
    return `https://www.zhuanglz.cn:8443${imageUrl}`;
  },



  // Banner点击
  onBannerTap: function(e) {
    const id = e.currentTarget.dataset.id;
    const banner = this.data.banners.find(item => item.id === id);

    if (banner.type === 'repair') {
      this.goToRepair();
    } else if (banner.type === 'product') {
      this.goToMall();
    } else if (banner.type === 'service') {
      this.goToServiceCenters();
    }
  },

  // 分类点击
  onCategoryTap: function(e) {
    const type = e.currentTarget.dataset.type;

    switch(type) {
      case 'repair':
        this.goToRepair();
        break;
      case 'mall':
        this.goToMall();
        break;
      case 'service':
        this.goToServiceCenters();
        break;
      case 'consult':
        this.goToConsult();
        break;
      case 'join':
        this.goToJoinUs();
        break;
    }
  },

  // 跳转到产品页面
  goToMall: function() {
    // 使用switchTab方法跳转到tabBar页面
    wx.switchTab({
      url: '/pages/mall/mall'
    });
  },

  // 跳转到产品详情页
  goToProductDetail: function(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/product_detail/product_detail?id=${id}`
    });
  },

  // 跳转到维修页面
  goToRepair: function() {
    // 使用switchTab方法跳转到tabBar页面
    wx.switchTab({
      url: '/pages/repair/repair'
    });
  },

  // 跳转到维修表单页面
  goToRepairForm: function(e) {
    const type = e.currentTarget.dataset.type;
    let url = '/pages/repair_form/repair_form';
    if (type) {
      url += `?type=${type}`;
    }
    wx.navigateTo({
      url: url
    });
  },

  // 跳转到服务网点页面
  goToServiceCenters: function() {
    wx.navigateTo({
      url: '/pages/service_centers/service_centers'
    });
  },

  // 跳转到在线咨询
  goToConsult: function() {
    wx.navigateTo({
      url: '/pages/customer_service/customer_service'
    });
  },

  // 跳转到合作加盟页面
  goToJoinUs: function() {
    wx.navigateTo({
      url: '/pages/join_us/join_us'
    });
  }
})
