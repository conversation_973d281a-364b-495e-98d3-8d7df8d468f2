/* 严格按照抖音小程序address_list模板设计 */
.container {
  padding: 30rpx;
  padding-bottom: 150rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-state image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  color: #999;
  font-size: 28rpx;
}

/* 地址列表 */
.address-item {
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.address-item.default {
  border-left: 8rpx solid #1e88e5;
}

.address-content {
  padding: 30rpx;
}

.address-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.name-phone {
  display: flex;
  align-items: center;
}

.name {
  font-size: 32rpx;
  font-weight: 500;
  margin-right: 20rpx;
}

.phone {
  font-size: 28rpx;
  color: #666;
}

.default-tag {
  font-size: 22rpx;
  color: #1e88e5;
  border: 1px solid #1e88e5;
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
}

.address-detail {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}

.address-actions {
  display: flex;
  border-top: 1px solid #f0f0f0;
  height: 88rpx;
}

.action-btn {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 26rpx;
  color: #666;
}

.action-btn text {
  margin-left: 8rpx;
}

.action-divider {
  width: 1px;
  height: 40rpx;
  background-color: #f0f0f0;
  align-self: center;
}

.checkbox {
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  border: 1px solid #ddd;
  margin-right: 8rpx;
  position: relative;
}

.checkbox.checked {
  background-color: #1e88e5;
  border-color: #1e88e5;
}

.checkbox.checked:after {
  content: '';
  position: absolute;
  width: 16rpx;
  height: 8rpx;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(-45deg);
  top: 8rpx;
  left: 6rpx;
}

/* 添加地址按钮 */
.add-address-btn {
  position: fixed;
  bottom: 30rpx;
  left: 30rpx;
  right: 30rpx;
  height: 90rpx;
  background-color: #1e88e5;
  color: #fff;
  border-radius: 45rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(30, 136, 229, 0.3);
}

.add-address-btn text {
  margin-right: 8rpx;
}

/* 地址表单弹窗 */
.address-form-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  visibility: hidden;
  opacity: 0;
  transition: all 0.3s ease;
}

.address-form-popup.show {
  visibility: visible;
  opacity: 1;
}

.form-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.form-container {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  border-top-left-radius: 24rpx;
  border-top-right-radius: 24rpx;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.address-form-popup.show .form-container {
  transform: translateY(0);
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1px solid #f0f0f0;
  font-size: 32rpx;
  font-weight: 500;
}

.close-btn {
  font-size: 48rpx;
  color: #999;
  line-height: 1;
}

.form-content {
  padding: 30rpx;
  max-height: 70vh;
  overflow-y: auto;
}

.form-group {
  margin-bottom: 30rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
}

.form-control {
  width: 100%;
  height: 88rpx;
  border: 1px solid #e0e0e0;
  border-radius: 8rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

textarea.form-control {
  height: 160rpx;
  padding: 16rpx 24rpx;
  line-height: 1.5;
}

.picker {
  width: 100%;
  height: 88rpx;
  border: 1px solid #e0e0e0;
  border-radius: 8rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}

.picker.placeholder {
  color: #999;
}

.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.tag-item {
  padding: 12rpx 32rpx;
  border: 1px solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #666;
}

.tag-item.active {
  background-color: #e3f2fd;
  color: #1e88e5;
  border-color: #1e88e5;
}

.switch-group {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.form-footer {
  padding: 30rpx;
  border-top: 1px solid #f0f0f0;
}

.btn {
  display: flex;
  align-items: center; 
  justify-content: center;
  height: 88rpx;
  line-height: 88rpx;
  font-size: 32rpx;
  border-radius: 44rpx;
  background-color: #1e88e5;
  color: white;
  border: none;
}

.btn-block {
  width: 100%;
}
