<!-- 严格按照抖音小程序address_list模板设计 -->
<view class="container">
  <!-- 地址列表 -->
  <view class="address-list">
    <view class="empty-state" wx:if="{{addresses.length === 0}}">
      <image src="/images/empty-address.png" mode="aspectFit"></image>
      <view class="empty-text">暂无保存的地址</view>
    </view>
    
    <view class="address-item {{item.isDefault ? 'default' : ''}}" wx:for="{{addresses}}" wx:key="id" bindtap="selectAddress" data-id="{{item.id}}">
      <view class="address-content">
        <view class="address-header">
          <view class="name-phone">
            <text class="name">{{item.name}}</text>
            <text class="phone">{{item.phone}}</text>
          </view>
          <view class="default-tag" wx:if="{{item.isDefault}}">默认</view>
        </view>
        <view class="address-detail">{{item.province}}{{item.city}}{{item.district}}{{item.address}}</view>
      </view>
      <view class="address-actions">
        <view class="action-btn" catchtap="setDefault" data-id="{{item.id}}">
          <view class="checkbox {{item.isDefault ? 'checked' : ''}}"></view>
          <text>设为默认</text>
        </view>
        <view class="action-divider"></view>
        <view class="action-btn" catchtap="editAddress" data-id="{{item.id}}">
          <text class="iconfont icon-edit">✏️</text>
          <text>编辑</text>
        </view>
        <view class="action-divider"></view>
        <view class="action-btn" catchtap="deleteAddress" data-id="{{item.id}}">
          <text class="iconfont icon-delete">🗑️</text>
          <text>删除</text>
        </view>
      </view>
    </view>
  </view>
  
  <!-- 添加地址按钮 -->
  <view class="add-address-btn" bindtap="showAddressForm">
    <text class="iconfont icon-plus">➕</text>
    <text>新增地址</text>
  </view>
  
  <!-- 地址表单弹窗 -->
  <view class="address-form-popup {{showForm ? 'show' : ''}}">
    <view class="form-mask" bindtap="hideAddressForm"></view>
    <view class="form-container">
      <view class="form-header">
        <text>{{editingAddress ? '编辑地址' : '新增地址'}}</text>
        <text class="close-btn" bindtap="hideAddressForm">×</text>
      </view>
      
      <view class="form-content">
        <view class="form-group">
          <view class="form-label">联系人</view>
          <input class="form-control" placeholder="请输入联系人姓名" value="{{formData.name}}" bindinput="onNameInput" />
        </view>
        
        <view class="form-group">
          <view class="form-label">手机号码</view>
          <input class="form-control" type="number" placeholder="请输入手机号码" value="{{formData.phone}}" bindinput="onPhoneInput" />
        </view>
        
        <view class="form-group">
          <view class="form-label">所在地区</view>
          <picker mode="region" bindchange="onRegionChange" value="{{formData.region}}">
            <view class="picker {{!formData.region[0] ? 'placeholder' : ''}}">
              {{formData.region[0] ? formData.region[0] + ' ' + formData.region[1] + ' ' + formData.region[2] : '请选择所在地区'}}
            </view>
          </picker>
        </view>
        
        <view class="form-group">
          <view class="form-label">详细地址</view>
          <textarea class="form-control" placeholder="请输入详细地址，如街道、小区、楼栋号、单元室等" value="{{formData.address}}" bindinput="onAddressInput" />
        </view>
        
        <view class="form-group">
          <view class="form-label">标签</view>
          <view class="tag-list">
            <view class="tag-item {{formData.tag === '家' ? 'active' : ''}}" bindtap="selectTag" data-tag="家">家</view>
            <view class="tag-item {{formData.tag === '公司' ? 'active' : ''}}" bindtap="selectTag" data-tag="公司">公司</view>
            <view class="tag-item {{formData.tag === '学校' ? 'active' : ''}}" bindtap="selectTag" data-tag="学校">学校</view>
            <view class="tag-item {{formData.tag === '其他' ? 'active' : ''}}" bindtap="selectTag" data-tag="其他">其他</view>
          </view>
        </view>
        
        <view class="form-group switch-group">
          <text>设为默认地址</text>
          <switch checked="{{formData.isDefault}}" bindchange="onDefaultChange" color="#1e88e5" />
        </view>
      </view>
      
      <view class="form-footer">
        <button class="btn btn-block" bindtap="saveAddress">保存</button>
      </view>
    </view>
  </view>
</view>
