// 严格按照抖音小程序address_list模板设计
const app = getApp()

Page({
  data: {
    addresses: [],
    showForm: false,
    editingAddress: null,
    formData: {
      id: '',
      name: '',
      phone: '',
      region: ['', '', ''],
      province: '',
      city: '',
      district: '',
      address: '',
      tag: '家',
      isDefault: false
    },
    fromPage: '', // 来源页面
    openId: '' // 用户openId
  },

  onLoad: function (options) {
    // 记录来源页面
    if (options.from) {
      this.setData({
        fromPage: options.from
      });
    }

    // 获取openId
    this.getOpenId();
  },

  onShow: function() {
    // 如果已经有openId，则获取地址列表
    if (this.data.openId) {
      this.getAddressList();
    }
  },

  // 获取openId
  getOpenId: function() {
    // 从全局数据中获取openId
    if (app.globalData.openId) {
      this.setData({
        openId: app.globalData.openId
      });
      this.getAddressList();
    } else {
      // 从本地存储中获取
      wx.getStorage({
        key: 'openId',
        success: (res) => {
          if (res.data) {
            this.setData({
              openId: res.data
            });
            this.getAddressList();
          } else {
            this.showLoginTip();
          }
        },
        fail: () => {
          this.showLoginTip();
        }
      });
    }
  },

  // 显示登录提示
  showLoginTip: function() {
    wx.showModal({
      title: '提示',
      content: '请先登录后再使用地址管理功能',
      confirmText: '去登录',
      success: (res) => {
        if (res.confirm) {
          wx.navigateBack();
        }
      }
    });
  },

  // 获取地址列表
  getAddressList: function() {
    if (!this.data.openId) {
      return;
    }

    wx.showLoading({
      title: '加载中...'
    });

    const api = require('../../utils/api');
    api.getAddressList(this.data.openId).then(res => {
      wx.hideLoading();

      if (res.success) {
        this.setData({
          addresses: res.addresses || []
        });
      } else {
        wx.showToast({
          title: '获取地址列表失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      wx.hideLoading();
      wx.showToast({
        title: '获取地址列表失败',
        icon: 'none'
      });
    });
  },

  // 选择地址
  selectAddress: function(e) {
    const addressId = e.currentTarget.dataset.id;
    const address = this.data.addresses.find(item => item.id === addressId);

    if (this.data.fromPage === 'repair_form') {
      // 如果是从维修表单页面来的，将选中的地址保存到全局数据
      app.globalData.selectedAddress = address;

      wx.navigateBack();
    }
  },

  // 设为默认地址
  setDefault: function(e) {
    const addressId = e.currentTarget.dataset.id;
    const addresses = this.data.addresses.map(item => ({
      ...item,
      isDefault: item.id === addressId
    }));
    
    this.setData({ addresses });
    
    wx.showToast({
      title: '设置成功',
      icon: 'success'
    });
  },

  // 编辑地址
  editAddress: function(e) {
    const addressId = e.currentTarget.dataset.id;
    const address = this.data.addresses.find(item => item.id === addressId);
    
    this.setData({
      editingAddress: address,
      formData: {
        ...address,
        region: [address.province, address.city, address.district]
      },
      showForm: true
    });
  },

  // 删除地址
  deleteAddress: function(e) {
    const addressId = e.currentTarget.dataset.id;
    
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这个地址吗？',
      success: (res) => {
        if (res.confirm) {
          const addresses = this.data.addresses.filter(item => item.id !== addressId);
          this.setData({ addresses });
          
          wx.showToast({
            title: '删除成功',
            icon: 'success'
          });
        }
      }
    });
  },

  // 显示地址表单
  showAddressForm: function() {
    this.setData({
      editingAddress: null,
      formData: {
        id: '',
        name: '',
        phone: '',
        region: ['', '', ''],
        province: '',
        city: '',
        district: '',
        address: '',
        tag: '家',
        isDefault: false
      },
      showForm: true
    });
  },

  // 隐藏地址表单
  hideAddressForm: function() {
    this.setData({
      showForm: false
    });
  },

  // 表单输入处理
  onNameInput: function(e) {
    this.setData({
      'formData.name': e.detail.value
    });
  },

  onPhoneInput: function(e) {
    this.setData({
      'formData.phone': e.detail.value
    });
  },

  onRegionChange: function(e) {
    const region = e.detail.value;
    this.setData({
      'formData.region': region,
      'formData.province': region[0],
      'formData.city': region[1],
      'formData.district': region[2]
    });
  },

  onAddressInput: function(e) {
    this.setData({
      'formData.address': e.detail.value
    });
  },

  selectTag: function(e) {
    const tag = e.currentTarget.dataset.tag;
    this.setData({
      'formData.tag': tag
    });
  },

  onDefaultChange: function(e) {
    this.setData({
      'formData.isDefault': e.detail.value
    });
  },

  // 保存地址
  saveAddress: function() {
    const { formData } = this.data;

    // 表单验证
    if (!formData.name.trim()) {
      wx.showToast({
        title: '请输入联系人姓名',
        icon: 'none'
      });
      return;
    }

    if (!formData.phone.trim()) {
      wx.showToast({
        title: '请输入手机号码',
        icon: 'none'
      });
      return;
    }

    if (!formData.region[0]) {
      wx.showToast({
        title: '请选择所在地区',
        icon: 'none'
      });
      return;
    }

    if (!formData.address.trim()) {
      wx.showToast({
        title: '请输入详细地址',
        icon: 'none'
      });
      return;
    }

    // 构建地址数据
    const addressData = {
      id: formData.id || null,
      openId: this.data.openId,
      name: formData.name,
      phone: formData.phone,
      province: formData.province,
      city: formData.city,
      district: formData.district,
      address: formData.address,
      tag: formData.tag,
      isDefault: formData.isDefault
    };

    wx.showLoading({
      title: '保存中...'
    });

    const api = require('../../utils/api');
    api.saveAddress(addressData).then(res => {
      wx.hideLoading();

      if (res.success) {
        // 更新地址列表
        this.getAddressList();

        // 隐藏表单
        this.setData({
          showForm: false
        });

        wx.showToast({
          title: res.message || '保存成功',
          icon: 'success'
        });
      } else {
        wx.showToast({
          title: res.message || '保存失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      wx.hideLoading();
      wx.showToast({
        title: '保存失败',
        icon: 'none'
      });
    });
  }
})
