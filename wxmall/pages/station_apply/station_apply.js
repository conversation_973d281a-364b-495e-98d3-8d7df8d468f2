const app = getApp()
const api = require('../../utils/api.js')

Page({
  data: {
    formData: {
      name: '',
      contactPerson: '',
      phone: '',
      email: '',
      address: '',
      latitude: '',
      longitude: '',
      businessHours: '',
      serviceDescription: '',
      serviceFee: '',
      inspectionFee: '',
      parkingInfo: '',
      businessLicense: '',
      qualificationCertificates: [],
      images: []
    },
    serviceTypeOptions: [
      { id: 1, name: '充电桩维修', checked: false },
      { id: 2, name: '充电桩安装', checked: false },
      { id: 3, name: '充电桩检测', checked: false },
      { id: 4, name: '充电桩保养', checked: false },
      { id: 5, name: '技术咨询', checked: false }
    ],
    equipmentTypeOptions: [
      { id: 1, name: '交流充电桩', checked: false },
      { id: 2, name: '直流充电桩', checked: false },
      { id: 3, name: '便携式充电器', checked: false },
      { id: 4, name: '充电枪', checked: false },
      { id: 5, name: '充电线缆', checked: false }
    ],
    facilityOptions: [
      { id: 1, name: '停车场', checked: false },
      { id: 2, name: '休息区', checked: false },
      { id: 3, name: '卫生间', checked: false },
      { id: 4, name: '便利店', checked: false },
      { id: 5, name: '免费WiFi', checked: false },
      { id: 6, name: '监控设备', checked: false }
    ],
    submitting: false
  },

  onLoad: function () {
    console.log('充电站申请页面加载');
  },

  onShow: function () {
    console.log('充电站申请页面显示');
  },

  // 输入框变化
  onInputChange: function(e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;
    
    this.setData({
      [`formData.${field}`]: value
    });
  },

  // 选择位置
  chooseLocation: function() {
    wx.chooseLocation({
      success: (res) => {
        console.log('选择位置成功:', res);
        this.setData({
          'formData.address': res.address,
          'formData.latitude': res.latitude,
          'formData.longitude': res.longitude
        });
      },
      fail: (err) => {
        console.error('选择位置失败:', err);
        if (err.errMsg.includes('auth deny')) {
          wx.showModal({
            title: '需要位置权限',
            content: '请在设置中开启位置权限',
            showCancel: false
          });
        }
      }
    });
  },

  // 服务类型变化
  onServiceTypeChange: function(e) {
    const index = e.currentTarget.dataset.index;
    const serviceTypeOptions = this.data.serviceTypeOptions;
    serviceTypeOptions[index].checked = !serviceTypeOptions[index].checked;
    
    this.setData({
      serviceTypeOptions: serviceTypeOptions
    });
  },

  // 设备类型变化
  onEquipmentTypeChange: function(e) {
    const index = e.currentTarget.dataset.index;
    const equipmentTypeOptions = this.data.equipmentTypeOptions;
    equipmentTypeOptions[index].checked = !equipmentTypeOptions[index].checked;
    
    this.setData({
      equipmentTypeOptions: equipmentTypeOptions
    });
  },

  // 设施变化
  onFacilityChange: function(e) {
    const index = e.currentTarget.dataset.index;
    const facilityOptions = this.data.facilityOptions;
    facilityOptions[index].checked = !facilityOptions[index].checked;
    
    this.setData({
      facilityOptions: facilityOptions
    });
  },

  // 上传营业执照
  uploadBusinessLicense: function() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePath = res.tempFilePaths[0];
        this.uploadImageToServer(tempFilePath, 'businessLicense');
      },
      fail: (err) => {
        console.error('选择图片失败:', err);
      }
    });
  },

  // 预览营业执照
  previewBusinessLicense: function(e) {
    const src = e.currentTarget.dataset.src;
    wx.previewImage({
      current: src,
      urls: [src]
    });
  },

  // 上传资质证书
  uploadCertificates: function() {
    const maxCount = 9 - this.data.formData.qualificationCertificates.length;
    if (maxCount <= 0) {
      wx.showToast({
        title: '最多上传9张证书',
        icon: 'none'
      });
      return;
    }

    wx.chooseImage({
      count: maxCount,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        res.tempFilePaths.forEach(tempFilePath => {
          this.uploadImageToServer(tempFilePath, 'qualificationCertificates');
        });
      },
      fail: (err) => {
        console.error('选择图片失败:', err);
      }
    });
  },

  // 预览证书
  previewCertificate: function(e) {
    const src = e.currentTarget.dataset.src;
    const index = e.currentTarget.dataset.index;
    wx.previewImage({
      current: src,
      urls: this.data.formData.qualificationCertificates
    });
  },

  // 删除证书
  deleteCertificate: function(e) {
    const index = e.currentTarget.dataset.index;
    const certificates = this.data.formData.qualificationCertificates;
    certificates.splice(index, 1);
    
    this.setData({
      'formData.qualificationCertificates': certificates
    });
  },

  // 上传网点图片
  uploadImages: function() {
    const maxCount = 9 - this.data.formData.images.length;
    if (maxCount <= 0) {
      wx.showToast({
        title: '最多上传9张图片',
        icon: 'none'
      });
      return;
    }

    wx.chooseImage({
      count: maxCount,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        res.tempFilePaths.forEach(tempFilePath => {
          this.uploadImageToServer(tempFilePath, 'images');
        });
      },
      fail: (err) => {
        console.error('选择图片失败:', err);
      }
    });
  },

  // 预览网点图片
  previewStationImage: function(e) {
    const src = e.currentTarget.dataset.src;
    const index = e.currentTarget.dataset.index;
    wx.previewImage({
      current: src,
      urls: this.data.formData.images
    });
  },

  // 删除网点图片
  deleteImage: function(e) {
    const index = e.currentTarget.dataset.index;
    const images = this.data.formData.images;
    images.splice(index, 1);
    
    this.setData({
      'formData.images': images
    });
  },

  // 上传图片到服务器
  uploadImageToServer: function(tempFilePath, field) {
    wx.showLoading({
      title: '上传中...'
    });

    wx.uploadFile({
      url: 'https://www.zhuanglz.cn:8443/api/upload/image',
      filePath: tempFilePath,
      name: 'file',
      success: (res) => {
        wx.hideLoading();
        try {
          const data = JSON.parse(res.data);
          if (data.success) {
            const imageUrl = data.data.url;
            
            if (field === 'businessLicense') {
              this.setData({
                'formData.businessLicense': imageUrl
              });
            } else if (field === 'qualificationCertificates') {
              const certificates = this.data.formData.qualificationCertificates;
              certificates.push(imageUrl);
              this.setData({
                'formData.qualificationCertificates': certificates
              });
            } else if (field === 'images') {
              const images = this.data.formData.images;
              images.push(imageUrl);
              this.setData({
                'formData.images': images
              });
            }
            
            wx.showToast({
              title: '上传成功',
              icon: 'success'
            });
          } else {
            throw new Error(data.message || '上传失败');
          }
        } catch (e) {
          console.error('上传响应解析失败:', e);
          wx.showToast({
            title: '上传失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('上传失败:', err);
        wx.showToast({
          title: '上传失败',
          icon: 'none'
        });
      }
    });
  },

  // 验证表单
  validateForm: function() {
    const { formData } = this.data;

    if (!formData.name.trim()) {
      wx.showToast({ title: '请输入网点名称', icon: 'none' });
      return false;
    }

    if (!formData.contactPerson.trim()) {
      wx.showToast({ title: '请输入联系人', icon: 'none' });
      return false;
    }

    if (!formData.phone.trim()) {
      wx.showToast({ title: '请输入联系电话', icon: 'none' });
      return false;
    }

    if (!/^1[3-9]\d{9}$/.test(formData.phone)) {
      wx.showToast({ title: '请输入正确的手机号', icon: 'none' });
      return false;
    }

    if (!formData.address.trim()) {
      wx.showToast({ title: '请选择网点地址', icon: 'none' });
      return false;
    }

    // 检查服务类型
    const selectedServiceTypes = this.data.serviceTypeOptions.filter(item => item.checked);
    if (selectedServiceTypes.length === 0) {
      wx.showToast({ title: '请选择至少一种服务类型', icon: 'none' });
      return false;
    }

    if (!formData.businessLicense) {
      wx.showToast({ title: '请上传营业执照', icon: 'none' });
      return false;
    }

    return true;
  },

  // 提交申请
  submitApplication: function() {
    if (!this.validateForm()) {
      return;
    }

    this.setData({ submitting: true });

    // 准备提交数据
    const submitData = {
      ...this.data.formData,
      serviceTypes: this.data.serviceTypeOptions.filter(item => item.checked).map(item => item.name),
      equipmentTypes: this.data.equipmentTypeOptions.filter(item => item.checked).map(item => item.name),
      facilities: this.data.facilityOptions.filter(item => item.checked).map(item => item.name)
    };

    console.log('提交数据:', submitData);

    api.submitServiceCenterApplication(submitData).then(res => {
      this.setData({ submitting: false });

      if (res.success) {
        wx.showModal({
          title: '申请提交成功',
          content: '您的申请已提交，我们将在3-5个工作日内完成审核，请保持电话畅通。',
          showCancel: false,
          success: () => {
            wx.navigateBack();
          }
        });
      } else {
        wx.showToast({
          title: res.message || '提交失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      this.setData({ submitting: false });
      console.error('提交申请失败:', err);
      wx.showToast({
        title: '提交失败，请重试',
        icon: 'none'
      });
    });
  },

  // 页面分享
  onShareAppMessage: function () {
    return {
      title: '桩郎中充电站入驻申请',
      path: '/pages/station_apply/station_apply',
      imageUrl: '/images/share/station_apply.png'
    };
  },

  // 页面分享到朋友圈
  onShareTimeline: function () {
    return {
      title: '桩郎中充电站入驻申请',
      imageUrl: '/images/share/station_apply.png'
    };
  }
})
