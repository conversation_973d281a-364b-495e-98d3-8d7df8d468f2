page {
  background-color: #f5f5f5;
}

.container {
  height: 100vh;
  padding: 20rpx;
  box-sizing: border-box;
}

/* 申请须知 */
.notice {
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 30rpx;
}

.notice-title {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  font-weight: 600;
  color: #856404;
  margin-bottom: 16rpx;
}

.notice-title text:first-child {
  margin-right: 8rpx;
}

.notice-content {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.notice-content text {
  font-size: 24rpx;
  color: #856404;
  line-height: 1.4;
}

/* 表单区域 */
.form-section {
  background-color: white;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 32rpx;
  padding-bottom: 16rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.form-item {
  margin-bottom: 32rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 12rpx;
  font-weight: 500;
}

.required {
  color: #ff4757;
  margin-left: 4rpx;
}

.form-input {
  width: 100%;
  height: 80rpx;
  padding: 0 24rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 28rpx;
  background-color: #fafafa;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #1e88e5;
  background-color: white;
}

.form-textarea {
  width: 100%;
  min-height: 120rpx;
  padding: 16rpx 24rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 28rpx;
  background-color: #fafafa;
  box-sizing: border-box;
}

.form-textarea:focus {
  border-color: #1e88e5;
  background-color: white;
}

/* 位置选择器 */
.location-picker {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80rpx;
  padding: 0 24rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  background-color: #fafafa;
}

.location-text {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.location-text.placeholder {
  color: #999;
}

.location-icon {
  font-size: 32rpx;
  color: #1e88e5;
}

/* 复选框组 */
.checkbox-group {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.checkbox-item {
  display: flex;
  align-items: center;
  padding: 12rpx 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 20rpx;
  background-color: #fafafa;
  transition: all 0.3s;
}

.checkbox-item.checked {
  border-color: #1e88e5;
  background-color: #e3f2fd;
}

.checkbox-icon {
  width: 32rpx;
  height: 32rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 6rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12rpx;
  font-size: 20rpx;
  color: white;
  background-color: white;
}

.checkbox-item.checked .checkbox-icon {
  border-color: #1e88e5;
  background-color: #1e88e5;
}

.checkbox-text {
  font-size: 26rpx;
  color: #333;
}

/* 上传区域 */
.upload-area {
  position: relative;
  width: 100%;
  height: 200rpx;
  border: 2rpx dashed #e0e0e0;
  border-radius: 12rpx;
  overflow: hidden;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #999;
  background-color: #fafafa;
}

.upload-placeholder text:first-child {
  font-size: 48rpx;
  margin-bottom: 8rpx;
}

.upload-placeholder text:last-child {
  font-size: 24rpx;
}

.uploaded-image {
  width: 100%;
  height: 100%;
}

.reupload-btn {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  width: 48rpx;
  height: 48rpx;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24rpx;
}

/* 上传网格 */
.upload-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
}

.upload-item {
  position: relative;
  width: 100%;
  height: 200rpx;
  border-radius: 12rpx;
  overflow: hidden;
}

.upload-item.upload-placeholder {
  border: 2rpx dashed #e0e0e0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #fafafa;
  color: #999;
}

.upload-item.upload-placeholder text:first-child {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}

.upload-item.upload-placeholder text:last-child {
  font-size: 20rpx;
}

.delete-btn {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 32rpx;
  height: 32rpx;
  background-color: rgba(255, 71, 87, 0.8);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20rpx;
  z-index: 2;
}

.image-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 32rpx;
  opacity: 0;
  transition: opacity 0.3s;
}

.upload-item:active .image-mask {
  opacity: 1;
}

.upload-tip {
  font-size: 24rpx;
  color: #666;
  margin-top: 12rpx;
  line-height: 1.4;
}

/* 提交区域 */
.submit-section {
  padding: 40rpx 0;
  text-align: center;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  background-color: #1e88e5;
  color: white;
  border: none;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 20rpx;
}

.submit-btn.submitting {
  background-color: #ccc;
}

.submit-tip {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

/* 底部安全区域 */
.safe-area {
  height: 40rpx;
}
