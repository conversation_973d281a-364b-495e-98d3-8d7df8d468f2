const app = getApp()
const api = require('../../utils/api.js')

Page({
  data: {
    longitude: 121.4737,
    latitude: 31.2304,
    scale: 12,
    markers: [],
    serviceCenters: [],
    allServiceCenters: [],
    sortType: 'all',
    hasMore: false,
    loading: false,
    showDetail: false,
    selectedServiceCenter: null
  },

  onLoad: function () {

    
    // 获取用户位置
    this.getUserLocation();

    // 加载服务网点数据
    this.loadServiceCenters();
  },

  onShow: function () {

  },

  // 获取用户位置
  getUserLocation: function() {
    wx.getLocation({
      type: 'gcj02',
      success: (res) => {

        this.setData({
          latitude: res.latitude,
          longitude: res.longitude
        });

        // 更新服务网点距离
        this.updateServiceCenterDistance(res.latitude, res.longitude);

        // 更新地图标记
        this.updateMapMarkers();
      },
      fail: (err) => {

        wx.showToast({
          title: '获取位置失败',
          icon: 'none'
        });
      }
    });
  },

  // 加载服务网点数据
  loadServiceCenters: function() {
    this.setData({ loading: true });

    api.getApprovedServiceCenters().then(res => {
      if (res.success && res.data) {

        
        // 处理服务网点数据
        const serviceCenters = res.data.map(center => {
          // 解析JSON字段
          let serviceTypes = [];
          let facilities = [];

          try {
            serviceTypes = typeof center.serviceTypes === 'string' ? 
              JSON.parse(center.serviceTypes) : (center.serviceTypes || []);
            facilities = typeof center.facilities === 'string' ? 
              JSON.parse(center.facilities) : (center.facilities || []);
          } catch (e) {

          }

          return {
            id: center.id,
            name: center.name,
            address: center.address,
            phone: center.phone,
            businessHours: center.businessHours,
            rating: center.rating || 5.0,
            reviewCount: center.reviewCount || 0,
            distance: 0, // 初始距离，后续计算
            latitude: parseFloat(center.latitude),
            longitude: parseFloat(center.longitude),
            serviceTypes: serviceTypes,
            facilities: facilities,
            serviceFee: center.serviceFee || 0,
            inspectionFee: center.inspectionFee || 0,
            parkingInfo: center.parkingInfo || '',
            serviceDescription: center.serviceDescription || ''
          };
        });

        // 创建地图标记
        const markers = serviceCenters.map(item => {
          return {
            id: item.id,
            latitude: item.latitude,
            longitude: item.longitude,
            title: item.name,
            iconPath: '/images/icons/marker.png', // 地图标记图标
            width: 32,
            height: 32
          };
        });

        this.setData({
          serviceCenters: serviceCenters,
          allServiceCenters: serviceCenters,
          markers: markers,
          hasMore: false,
          loading: false
        });

        // 如果已经获取到用户位置，更新距离
        if (this.data.latitude !== 31.2304) {
          this.updateServiceCenterDistance(this.data.latitude, this.data.longitude);
        }
      } else {
        // 使用默认数据
        this.setDefaultServiceCenters();
      }
    }).catch(err => {

      // API失败时使用默认数据
      this.setDefaultServiceCenters();
    });
  },

  // 设置默认服务网点数据
  setDefaultServiceCenters: function() {
    const defaultServiceCenters = [
      {
        id: 1,
        name: '桩郎中成都总店',
        address: '成都市高新区天府大道中段1388号',
        phone: '028-12345678',
        businessHours: '08:00-18:00',
        rating: 4.8,
        reviewCount: 156,
        distance: 0,
        latitude: 30.5728,
        longitude: 104.0668,
        serviceTypes: ['充电桩维修', '充电桩安装', '技术咨询'],
        facilities: ['停车场', '休息区', '免费WiFi'],
        serviceFee: 50,
        inspectionFee: 30,
        parkingInfo: '免费停车2小时',
        serviceDescription: '专业充电桩维修服务，经验丰富的技术团队'
      },
      {
        id: 2,
        name: '桩郎中双流分店',
        address: '成都市双流区东升街道',
        phone: '028-87654321',
        businessHours: '09:00-17:00',
        rating: 4.6,
        reviewCount: 89,
        distance: 0,
        latitude: 30.5745,
        longitude: 103.9477,
        serviceTypes: ['充电桩维修', '配件更换'],
        facilities: ['停车场', '休息区'],
        serviceFee: 50,
        inspectionFee: 30,
        parkingInfo: '免费停车1小时',
        serviceDescription: '提供快速维修服务'
      }
    ];

    const markers = defaultServiceCenters.map(item => ({
      id: item.id,
      latitude: item.latitude,
      longitude: item.longitude,
      title: item.name,
      iconPath: '/images/icons/marker.png',
      width: 32,
      height: 32
    }));

    this.setData({
      serviceCenters: defaultServiceCenters,
      allServiceCenters: defaultServiceCenters,
      markers: markers,
      hasMore: false,
      loading: false
    });
  },

  // 更新服务网点距离
  updateServiceCenterDistance: function(latitude, longitude) {
    const serviceCenters = this.data.serviceCenters.map(item => {
      // 计算距离
      const distance = this.calculateDistance(
        latitude, longitude,
        item.latitude, item.longitude
      );

      return {
        ...item,
        distance: distance.toFixed(1)
      };
    });

    this.setData({
      serviceCenters: serviceCenters,
      allServiceCenters: serviceCenters
    });
  },

  // 计算两点之间的距离
  calculateDistance: function(lat1, lon1, lat2, lon2) {
    const R = 6371; // 地球半径，单位km
    const dLat = this.deg2rad(lat2 - lat1);
    const dLon = this.deg2rad(lon2 - lon1);
    const a =
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(this.deg2rad(lat1)) * Math.cos(this.deg2rad(lat2)) *
      Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    const d = R * c; // 距离，单位km
    return d;
  },

  // 角度转弧度
  deg2rad: function(deg) {
    return deg * (Math.PI/180);
  },

  // 更新地图标记
  updateMapMarkers: function() {
    const markers = this.data.serviceCenters.map(item => {
      return {
        id: item.id,
        latitude: item.latitude,
        longitude: item.longitude,
        title: item.name,
        iconPath: '/images/icons/marker.png',
        width: 32,
        height: 32
      };
    });

    this.setData({
      markers: markers
    });
  },

  // 排序方式点击
  onSortTap: function(e) {
    const type = e.currentTarget.dataset.type;

    if (type === this.data.sortType) {
      return;
    }

    this.setData({
      sortType: type
    });

    // 根据排序方式排序
    this.sortServiceCenters(type);
  },

  // 排序服务网点
  sortServiceCenters: function(type) {
    let sortedCenters = [...this.data.serviceCenters];

    switch(type) {
      case 'distance':
        // 按距离排序
        sortedCenters.sort((a, b) => parseFloat(a.distance) - parseFloat(b.distance));
        break;
      case 'rating':
        // 按评分排序
        sortedCenters.sort((a, b) => b.rating - a.rating);
        break;
      default:
        // 默认排序（按ID）
        sortedCenters.sort((a, b) => a.id - b.id);
        break;
    }

    this.setData({
      serviceCenters: sortedCenters
    });
  },

  // 地图标记点击
  onMarkerTap: function(e) {
    const markerId = e.detail.markerId;
    const serviceCenter = this.data.serviceCenters.find(item => item.id === markerId);

    if (serviceCenter) {
      this.setData({
        selectedServiceCenter: serviceCenter,
        showDetail: true
      });
    }
  },

  // 服务网点卡片点击
  onServiceCenterTap: function(e) {
    const id = e.currentTarget.dataset.id;
    const serviceCenter = this.data.serviceCenters.find(item => item.id === id);

    if (serviceCenter) {
      this.setData({
        selectedServiceCenter: serviceCenter,
        showDetail: true
      });
    }
  },

  // 查看服务网点详情
  viewServiceCenterDetail: function(e) {
    const id = e.currentTarget.dataset.id;
    const serviceCenter = this.data.serviceCenters.find(item => item.id === id);

    if (serviceCenter) {
      this.setData({
        selectedServiceCenter: serviceCenter,
        showDetail: true
      });
    }
  },

  // 隐藏服务网点详情
  hideServiceCenterDetail: function() {
    this.setData({
      showDetail: false,
      selectedServiceCenter: null
    });
  },

  // 阻止冒泡
  preventBubble: function() {
    // 阻止事件冒泡
  },

  // 拨打电话
  callServiceCenter: function(e) {
    const phone = e.currentTarget.dataset.phone;

    if (!phone) {
      wx.showToast({
        title: '电话号码不存在',
        icon: 'none'
      });
      return;
    }

    wx.makePhoneCall({
      phoneNumber: phone,
      success: () => {

      },
      fail: (err) => {

        wx.showToast({
          title: '拨打电话失败',
          icon: 'none'
        });
      }
    });
  },

  // 导航到服务网点
  navigateToServiceCenter: function(e) {
    const { latitude, longitude, name } = e.currentTarget.dataset;

    if (!latitude || !longitude) {
      wx.showToast({
        title: '位置信息不存在',
        icon: 'none'
      });
      return;
    }

    wx.openLocation({
      latitude: parseFloat(latitude),
      longitude: parseFloat(longitude),
      name: name || '服务网点',
      address: name || '服务网点',
      scale: 18,
      success: () => {

      },
      fail: (err) => {

        wx.showToast({
          title: '打开地图失败',
          icon: 'none'
        });
      }
    });
  },

  // 页面分享
  onShareAppMessage: function () {
    return {
      title: '桩郎中服务网点',
      path: '/pages/service_centers/service_centers',
      imageUrl: '/images/share/service_centers.png'
    };
  },

  // 页面分享到朋友圈
  onShareTimeline: function () {
    return {
      title: '桩郎中服务网点 - 专业充电桩维修服务',
      imageUrl: '/images/share/service_centers.png'
    };
  }
})
