<view class="container">
  <!-- 页面标题 -->
  <view class="page-header">
    <view class="page-title">服务网点</view>
    <view class="page-subtitle">就近选择专业服务网点</view>
  </view>

  <!-- 地图容器 -->
  <view class="map-section">
    <view class="map-header">
      <text class="map-title">网点分布</text>
      <view class="location-btn" bindtap="getUserLocation">
        <text>定位</text>
      </view>
    </view>
    <view class="map-container">
      <map id="map"
           longitude="{{longitude}}"
           latitude="{{latitude}}"
           scale="{{scale}}"
           markers="{{markers}}"
           show-location="{{true}}"
           bindmarkertap="onMarkerTap">
      </map>
    </view>
  </view>

  <!-- 筛选标签 -->
  <view class="filter-section">
    <view class="filter-tabs">
      <view class="filter-tab {{sortType === 'all' ? 'active' : ''}}" bindtap="onSortTap" data-type="all">
        <text>全部网点</text>
      </view>
      <view class="filter-tab {{sortType === 'distance' ? 'active' : ''}}" bindtap="onSortTap" data-type="distance">
        <text>距离最近</text>
      </view>
      <view class="filter-tab {{sortType === 'rating' ? 'active' : ''}}" bindtap="onSortTap" data-type="rating">
        <text>评分最高</text>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">正在加载服务网点...</text>
  </view>

  <!-- 服务网点列表 -->
  <view class="service-list" wx:if="{{!loading}}">
    <view class="service-card" wx:for="{{serviceCenters}}" wx:key="id" bindtap="onServiceCenterTap" data-id="{{item.id}}">
      <!-- 网点基本信息 -->
      <view class="service-header">
        <view class="service-name">{{item.name}}</view>
        <view class="service-distance" wx:if="{{item.distance}}">{{item.distance}}km</view>
      </view>

      <!-- 网点详细信息 -->
      <view class="service-info">
        <view class="info-item">
          <text class="info-text">{{item.address}}</text>
        </view>
        <view class="info-item">
          <text class="info-text">{{item.phone}}</text>
        </view>
        <view class="info-item">
          <text class="info-text">{{item.businessHours}}</text>
        </view>
        <view class="info-item" wx:if="{{item.rating}}">
          <text class="info-text">{{item.rating}}分 ({{item.reviewCount || 0}}条评价)</text>
        </view>
      </view>

      <!-- 服务类型标签 -->
      <view class="service-tags" wx:if="{{item.serviceTypes && item.serviceTypes.length > 0}}">
        <text class="service-tag" wx:for="{{item.serviceTypes}}" wx:key="*this" wx:for-item="tag">{{tag}}</text>
      </view>

      <!-- 设施信息 -->
      <view class="facilities" wx:if="{{item.facilities && item.facilities.length > 0}}">
        <text class="facilities-label">设施：</text>
        <text class="facility-item" wx:for="{{item.facilities}}" wx:key="*this" wx:for-item="facility">{{facility}}</text>
      </view>

      <!-- 费用信息 -->
      <view class="fee-info" wx:if="{{item.serviceFee || item.inspectionFee}}">
        <view class="fee-item" wx:if="{{item.serviceFee}}">
          <text class="fee-label">服务费：</text>
          <text class="fee-value">¥{{item.serviceFee}}</text>
        </view>
        <view class="fee-item" wx:if="{{item.inspectionFee}}">
          <text class="fee-label">检测费：</text>
          <text class="fee-value">¥{{item.inspectionFee}}</text>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="service-actions">
        <view class="action-btn call-btn" catchtap="callServiceCenter" data-phone="{{item.phone}}">
          <text class="btn-text">电话</text>
        </view>
        <view class="action-btn navigate-btn" catchtap="navigateToServiceCenter" 
              data-latitude="{{item.latitude}}" 
              data-longitude="{{item.longitude}}" 
              data-name="{{item.name}}">
          <text class="btn-text">导航</text>
        </view>
        <view class="action-btn detail-btn" catchtap="viewServiceCenterDetail" data-id="{{item.id}}">
          <text class="btn-text">详情</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{!loading && serviceCenters.length === 0}}">
    <view class="empty-icon">🏪</view>
    <view class="empty-title">暂无服务网点</view>
    <view class="empty-desc">当前区域暂无服务网点</view>
  </view>

  <!-- 加载更多 -->
  <view class="load-more" wx:if="{{hasMore && !loading && serviceCenters.length > 0}}">
    <text class="load-more-text">正在加载更多...</text>
  </view>
  
  <!-- 没有更多 -->
  <view class="no-more" wx:if="{{!loading && serviceCenters.length > 0 && !hasMore}}">
    <text class="no-more-text">已显示全部服务网点</text>
  </view>

  <!-- 底部安全区域 -->
  <view class="safe-area"></view>
</view>

<!-- 服务网点详情弹窗 -->
<view class="detail-modal {{showDetail ? 'show' : ''}}" bindtap="hideServiceCenterDetail">
  <view class="detail-content" catchtap="preventBubble" wx:if="{{selectedServiceCenter}}">
    <view class="detail-header">
      <view class="detail-title">{{selectedServiceCenter.name}}</view>
      <view class="detail-close" bindtap="hideServiceCenterDetail">✕</view>
    </view>
    
    <view class="detail-body">
      <view class="detail-section">
        <view class="detail-label">地址</view>
        <view class="detail-value">{{selectedServiceCenter.address}}</view>
      </view>
      
      <view class="detail-section">
        <view class="detail-label">联系电话</view>
        <view class="detail-value">{{selectedServiceCenter.phone}}</view>
      </view>
      
      <view class="detail-section">
        <view class="detail-label">营业时间</view>
        <view class="detail-value">{{selectedServiceCenter.businessHours}}</view>
      </view>
      
      <view class="detail-section" wx:if="{{selectedServiceCenter.serviceDescription}}">
        <view class="detail-label">服务说明</view>
        <view class="detail-value">{{selectedServiceCenter.serviceDescription}}</view>
      </view>
      
      <view class="detail-section" wx:if="{{selectedServiceCenter.parkingInfo}}">
        <view class="detail-label">停车信息</view>
        <view class="detail-value">{{selectedServiceCenter.parkingInfo}}</view>
      </view>
    </view>
    
    <view class="detail-footer">
      <view class="detail-btn call-detail-btn" bindtap="callServiceCenter" data-phone="{{selectedServiceCenter.phone}}">
        <text>拨打电话</text>
      </view>
      <view class="detail-btn navigate-detail-btn" bindtap="navigateToServiceCenter" 
            data-latitude="{{selectedServiceCenter.latitude}}" 
            data-longitude="{{selectedServiceCenter.longitude}}" 
            data-name="{{selectedServiceCenter.name}}">
        <text>前往导航</text>
      </view>
    </view>
  </view>
</view>
