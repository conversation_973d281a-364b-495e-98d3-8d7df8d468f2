.container {
  padding: 30rpx 30rpx 120rpx;
  background-color: #f5f5f5;
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.page-title {
  font-size: 40rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 12rpx;
}

.page-subtitle {
  font-size: 28rpx;
  color: #666;
}

/* 地图区域 */
.map-section {
  margin-bottom: 32rpx;
}

.map-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.map-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.location-btn {
  display: flex;
  align-items: center;
  background-color: #1e88e5;
  color: white;
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.location-icon {
  margin-right: 8rpx;
}

.map-container {
  width: 100%;
  height: 400rpx;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

map {
  width: 100%;
  height: 100%;
}

/* 筛选区域 */
.filter-section {
  margin-bottom: 32rpx;
}

.filter-tabs {
  display: flex;
  background-color: white;
  border-radius: 32rpx;
  padding: 8rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.filter-tab {
  flex: 1;
  text-align: center;
  padding: 20rpx 16rpx;
  border-radius: 24rpx;
  font-size: 28rpx;
  color: #666;
  transition: all 0.3s;
}

.filter-tab.active {
  background-color: #1e88e5;
  color: white;
  font-weight: 500;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #e0e0e0;
  border-top: 4rpx solid #1e88e5;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 24rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 服务网点列表 */
.service-list {
  margin-bottom: 32rpx;
}

.service-card {
  background-color: white;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
  transition: transform 0.2s, box-shadow 0.2s;
}

.service-card:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* 网点头部 */
.service-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.service-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
}

.service-distance {
  background-color: #e3f2fd;
  color: #1e88e5;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 24rpx;
  font-weight: 500;
}

/* 网点信息 */
.service-info {
  margin-bottom: 20rpx;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
  font-size: 28rpx;
}

.info-icon {
  width: 40rpx;
  text-align: center;
  margin-right: 16rpx;
  font-size: 28rpx;
}

.info-text {
  flex: 1;
  color: #666;
  line-height: 1.4;
}

/* 服务标签 */
.service-tags {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 20rpx;
}

.service-tag {
  background-color: #f0f0f0;
  color: #666;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 24rpx;
  margin-right: 12rpx;
  margin-bottom: 8rpx;
}

/* 设施信息 */
.facilities {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin-bottom: 20rpx;
  font-size: 26rpx;
}

.facilities-label {
  color: #666;
  margin-right: 12rpx;
}

.facility-item {
  background-color: #e8f5e8;
  color: #4caf50;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  margin-right: 8rpx;
  margin-bottom: 6rpx;
  font-size: 22rpx;
}

/* 费用信息 */
.fee-info {
  display: flex;
  margin-bottom: 20rpx;
}

.fee-item {
  display: flex;
  align-items: center;
  margin-right: 32rpx;
  font-size: 26rpx;
}

.fee-label {
  color: #666;
  margin-right: 8rpx;
}

.fee-value {
  color: #f44336;
  font-weight: 600;
}

/* 操作按钮 */
.service-actions {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20rpx 16rpx;
  border-radius: 16rpx;
  font-size: 24rpx;
  transition: all 0.2s;
}

.btn-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}

.btn-text {
  font-size: 24rpx;
}

.call-btn {
  background-color: #e3f2fd;
  color: #1e88e5;
}

.call-btn:active {
  background-color: #bbdefb;
}

.navigate-btn {
  background-color: #1e88e5;
  color: white;
}

.navigate-btn:active {
  background-color: #1976d2;
}

.detail-btn {
  background-color: #f0f0f0;
  color: #666;
}

.detail-btn:active {
  background-color: #e0e0e0;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 100rpx 40rpx;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
}

.empty-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 32rpx;
}

.empty-action {
  background-color: #1e88e5;
  color: white;
  padding: 20rpx 40rpx;
  border-radius: 32rpx;
  font-size: 28rpx;
  display: inline-block;
}

/* 加载更多 */
.load-more, .no-more {
  text-align: center;
  padding: 40rpx 0;
}

.load-more-text, .no-more-text {
  font-size: 28rpx;
  color: #999;
}

/* 底部安全区域 */
.safe-area {
  height: 40rpx;
}

/* 详情弹窗 */
.detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
  visibility: hidden;
  opacity: 0;
  transition: all 0.3s;
}

.detail-modal.show {
  visibility: visible;
  opacity: 1;
}

.detail-content {
  background-color: white;
  border-radius: 24rpx 24rpx 0 0;
  width: 100%;
  max-height: 80vh;
  overflow-y: auto;
  transform: translateY(100%);
  transition: transform 0.3s;
}

.detail-modal.show .detail-content {
  transform: translateY(0);
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1px solid #e0e0e0;
}

.detail-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.detail-close {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: #f0f0f0;
  color: #666;
  font-size: 28rpx;
}

.detail-body {
  padding: 32rpx;
}

.detail-section {
  margin-bottom: 32rpx;
}

.detail-label {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 12rpx;
}

.detail-value {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

.detail-footer {
  display: flex;
  padding: 32rpx;
  gap: 24rpx;
  border-top: 1px solid #e0e0e0;
}

.detail-btn {
  flex: 1;
  text-align: center;
  padding: 24rpx;
  border-radius: 32rpx;
  font-size: 32rpx;
  font-weight: 500;
}

.call-detail-btn {
  background-color: #e3f2fd;
  color: #1e88e5;
}

.navigate-detail-btn {
  background-color: #1e88e5;
  color: white;
}
