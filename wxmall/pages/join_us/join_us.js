const app = getApp()

Page({
  data: {
    
  },

  onLoad: function () {

  },

  onShow: function () {

  },

  // 跳转到充电站入驻
  goToStationApply: function() {
    wx.navigateTo({
      url: '/pages/station_apply/station_apply'
    });
  },

  // 跳转到工程师入驻
  goToEngineerApply: function() {
    wx.navigateTo({
      url: '/pages/engineer_apply/engineer_apply'
    });
  },

  // 页面分享
  onShareAppMessage: function () {
    return {
      title: '桩郎中合作加盟 - 共建充电桩服务生态',
      path: '/pages/join_us/join_us',
      imageUrl: '/images/share/join_us.png'
    };
  },

  // 页面分享到朋友圈
  onShareTimeline: function () {
    return {
      title: '桩郎中合作加盟 - 共建充电桩服务生态',
      imageUrl: '/images/share/join_us.png'
    };
  }
})
