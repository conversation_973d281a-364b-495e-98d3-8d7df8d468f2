/* 严格按照抖音小程序repair_list_processing模板设计 */
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header {
  background-color: #fff;
  padding: 30rpx;
  text-align: center;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
}

.title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.content {
  flex: 1;
  padding: 30rpx;
}

.order-list {
  margin-bottom: 40rpx;
}

.order-item {
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #eee;
}

.order-no {
  font-size: 28rpx;
  color: #666;
}

.order-status {
  font-size: 28rpx;
}

.status-text {
  padding: 6rpx 16rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
}

.status-processing {
  background-color: #f6ffed;
  color: #52c41a;
}

.engineer-info {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 2rpx solid #eee;
}

.engineer-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 20rpx;
}

.engineer-avatar image {
  width: 100%;
  height: 100%;
}

.engineer-details {
  flex: 1;
}

.engineer-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.engineer-phone {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.engineer-rating {
  display: flex;
  gap: 20rpx;
}

.rating-text,
.service-count {
  font-size: 22rpx;
  color: #999;
}

.contact-engineer {
  width: 60rpx;
  height: 60rpx;
  background-color: #52c41a;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.contact-icon {
  font-size: 24rpx;
  color: white;
}

.progress-info {
  padding: 20rpx 0;
  border-bottom: 2rpx solid #eee;
}

.progress-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 20rpx;
}

.progress-steps {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.progress-step {
  display: flex;
  align-items: center;
  position: relative;
}

.step-dot {
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background-color: #d9d9d9;
  margin-right: 20rpx;
  position: relative;
  z-index: 2;
}

.progress-step.completed .step-dot {
  background-color: #52c41a;
}

.progress-step:not(:last-child)::after {
  content: '';
  position: absolute;
  left: 10rpx;
  top: 20rpx;
  width: 2rpx;
  height: 36rpx;
  background-color: #d9d9d9;
  z-index: 1;
}

.progress-step.completed:not(:last-child)::after {
  background-color: #52c41a;
}

.step-text {
  font-size: 26rpx;
  color: #666;
}

.progress-step.completed .step-text {
  color: #333;
}

.progress-note {
  margin-top: 20rpx;
  padding: 16rpx;
  background-color: #f6ffed;
  border-radius: 8rpx;
}

.note-label {
  font-size: 24rpx;
  color: #52c41a;
  font-weight: 500;
}

.note-text {
  font-size: 24rpx;
  color: #666;
  margin-left: 8rpx;
}

.order-info {
  padding: 20rpx 0;
}

.info-item {
  display: flex;
  margin-bottom: 16rpx;
}

.label {
  width: 160rpx;
  color: #999;
  font-size: 26rpx;
}

.value {
  flex: 1;
  color: #333;
  font-size: 26rpx;
}

.address {
  word-break: break-all;
}

.order-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20rpx;
  border-top: 2rpx solid #eee;
}

.time {
  font-size: 24rpx;
  color: #999;
}

.actions {
  display: flex;
}

.btn {
  margin-left: 20rpx;
  font-size: 24rpx;
  padding: 8rpx 24rpx;
  border-radius: 8rpx;
}

.btn-default {
  background-color: #fff;
  color: #666;
  border: 2rpx solid #d9d9d9;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  color: #999;
  font-size: 28rpx;
  margin-bottom: 40rpx;
}
