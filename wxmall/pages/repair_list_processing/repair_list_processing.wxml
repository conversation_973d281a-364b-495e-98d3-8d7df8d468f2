<!-- 严格按照抖音小程序repair_list_processing模板设计 -->
<view class="container">
  <view class="header">
    <view class="title">维修中</view>
  </view>

  <view class="content">
    <block wx:if="{{orders.length > 0}}">
      <view class="order-list">
        <view class="order-item" wx:for="{{orders}}" wx:key="id" bindtap="goToOrderDetail" data-id="{{item.orderNo}}">
          <view class="order-header">
            <view class="order-no">订单号: {{item.orderNo}}</view>
            <view class="order-status">
              <text class="status-text status-processing">维修中</text>
            </view>
          </view>
          <view class="engineer-info" wx:if="{{item.engineer}}">
            <view class="engineer-avatar">
              <image src="{{item.engineer.avatar || '/images/default-avatar.png'}}" mode="aspectFill"></image>
            </view>
            <view class="engineer-details">
              <view class="engineer-name">{{item.engineer.name}}</view>
              <view class="engineer-phone">{{item.engineer.phone}}</view>
              <view class="engineer-rating">
                <text class="rating-text">评分: {{item.engineer.rating}}</text>
                <text class="service-count">服务次数: {{item.engineer.serviceCount}}</text>
              </view>
            </view>
            <view class="contact-engineer" catchtap="callEngineer" data-phone="{{item.engineer.phone}}">
              <text class="contact-icon">📞</text>
            </view>
          </view>
          <view class="progress-info" wx:if="{{item.progress}}">
            <view class="progress-title">维修进度</view>
            <view class="progress-steps">
              <view class="progress-step {{index <= item.progress.currentStep ? 'completed' : ''}}" wx:for="{{item.progress.steps}}" wx:key="*this">
                <view class="step-dot"></view>
                <view class="step-text">{{item}}</view>
              </view>
            </view>
            <view class="progress-note" wx:if="{{item.progress.note}}">
              <text class="note-label">备注:</text>
              <text class="note-text">{{item.progress.note}}</text>
            </view>
          </view>
          <view class="order-info">
            <view class="info-item">
              <text class="label">故障类型:</text>
              <text class="value">{{faultTypeMap[item.faultType] || '未知故障'}}</text>
            </view>
            <view class="info-item">
              <text class="label">充电桩型号:</text>
              <text class="value">{{item.model}}</text>
            </view>
            <view class="info-item">
              <text class="label">开始时间:</text>
              <text class="value">{{item.startTime}}</text>
            </view>
            <view class="info-item">
              <text class="label">服务地址:</text>
              <text class="value address">{{item.fullAddress}}</text>
            </view>
          </view>
          <view class="order-footer">
            <view class="time">开始维修: {{item.startedAtFormatted}}</view>
            <view class="actions">
              <button class="btn btn-default" catchtap="contactService">联系客服</button>
            </view>
          </view>
        </view>
      </view>
    </block>
    <view class="empty-state" wx:else>
      <image class="empty-icon" src="/images/icons/empty.png" mode="aspectFit"></image>
      <text class="empty-text">暂无维修中订单</text>
    </view>
  </view>
</view>
