const api = require('../../utils/api');

Page({
  data: {
    // 商品数据
    products: [],
    loading: false,
    loadingMore: false,
    hasMore: true,
    page: 1,
    pageSize: 10
  },

  onLoad: function () {
    console.log('商城页面加载');
    this.loadProducts();
  },

  onShow: function () {
    console.log('商城页面显示');
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 1
      })
    }
  },

  onReachBottom: function () {
    if (this.data.hasMore && !this.data.loadingMore) {
      this.loadMoreProducts();
    }
  },

  onPullDownRefresh: function () {
    this.refreshProducts();
  },

  // 加载商品列表
  loadProducts: function() {
    this.setData({
      loading: true,
      page: 1,
      products: []
    });

    // 获取商品列表
    api.getProductList().then(res => {
      if (res.success && res.data && res.data.length > 0) {
        const products = res.data.map(product => ({
          ...product,
          processedImage: this.processImageUrl(product.mainImage)
        }));

        this.setData({
          products: products,
          loading: false,
          hasMore: products.length >= this.data.pageSize,
          page: 1
        });
      } else {
        // 使用默认数据
        this.setData({
          products: this.getDefaultProducts(),
          loading: false,
          hasMore: false
        });
      }
    }).catch(err => {
      console.error('加载商品失败:', err);
      // API失败时使用默认数据
      this.setData({
        products: this.getDefaultProducts(),
        loading: false,
        hasMore: false
      });
    });
  },

  // 加载更多商品 - 参考抖音小程序设计
  loadMoreProducts: function() {
    this.setData({ loadingMore: true });

    api.getProductList(this.data.currentCategory, this.data.sortType, this.data.priceOrder, this.data.page + 1).then(res => {
      if (res.success && res.data && res.data.length > 0) {
        const newProducts = res.data.map(product => ({
          ...product,
          processedImage: this.processImageUrl(product.mainImage)
        }));

        this.setData({
          products: [...this.data.products, ...newProducts],
          loadingMore: false,
          hasMore: newProducts.length >= this.data.pageSize,
          page: this.data.page + 1
        });
      } else {
        this.setData({
          loadingMore: false,
          hasMore: false
        });
      }
    }).catch(err => {
      console.error('加载更多商品失败:', err);
      this.setData({
        loadingMore: false,
        hasMore: false
      });
    });
  },

  // 刷新商品列表
  refreshProducts: function() {
    this.setData({
      page: 1,
      hasMore: true
    });
    this.loadProducts();
    wx.stopPullDownRefresh();
  },

  // 处理图片URL
  processImageUrl: function(imageUrl) {
    if (!imageUrl || imageUrl.trim() === '') {
      return '/images/products/充电线缆.png';
    }

    if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
      return imageUrl;
    }

    if (imageUrl.startsWith('/uploads/')) {
      return `https://www.zhuanglz.cn:8443${imageUrl}`;
    }

    if (!imageUrl.startsWith('/')) {
      return `https://www.zhuanglz.cn:8443/uploads/${imageUrl}`;
    }

    return `https://www.zhuanglz.cn:8443${imageUrl}`;
  },

  // 获取默认商品数据
  getDefaultProducts: function() {
    return [
      {
        id: 1,
        name: '快充充电枪',
        price: 299.00,
        image: '/images/products/快充充电枪.png',
        description: '高品质快充充电枪，安全可靠'
      },
      {
        id: 2,
        name: '5米加长型充电线缆',
        price: 199.00,
        image: '/images/products/充电线缆.png',
        description: '加长型充电线缆，使用更方便'
      },
      {
        id: 3,
        name: '充电控制模块',
        price: 599.00,
        image: '/images/products/充电控制模块.png',
        description: '智能充电控制模块，稳定耐用'
      },
      {
        id: 4,
        name: '充电桩防水保护盒',
        price: 89.00,
        image: '/images/products/充电桩防水保护盒.png',
        description: '防水保护盒，延长设备寿命'
      }
    ];
  },

  // 跳转到商品详情
  goToProductDetail: function(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/product_detail/product_detail?id=${id}`
    });
  },

  // 页面分享
  onShareAppMessage: function () {
    return {
      title: '桩郎中商城 - 专业充电桩配件',
      path: '/pages/mall/mall',
      imageUrl: '/images/share/mall.png'
    };
  },

  // 页面分享到朋友圈
  onShareTimeline: function () {
    return {
      title: '桩郎中商城 - 专业充电桩配件',
      imageUrl: '/images/share/mall.png'
    };
  }
})
