<view class="container">
  <!-- 商品网格 -->
  <view class="products-grid">
    <!-- 加载状态 -->
    <view class="loading" wx:if="{{loading}}">
      <text>加载中...</text>
    </view>

    <!-- 商品列表 -->
    <view class="product-card"
          wx:for="{{products}}"
          wx:key="id"
          bindtap="goToProductDetail"
          data-id="{{item.id}}">
      <view class="product-image">
        <image src="{{item.processedImage}}" mode="aspectFill" />
      </view>
      <view class="product-info">
        <view class="product-name">{{item.name}}</view>
        <view class="product-description">{{item.description}}</view>
        <view class="product-footer">
          <view class="product-price">¥{{item.price}}</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载更多/没有更多 -->
  <view class="loading-more" wx:if="{{loadingMore}}">加载更多...</view>
  <view class="no-more" wx:if="{{!hasMore && products.length > 0}}">没有更多了</view>
</view>
