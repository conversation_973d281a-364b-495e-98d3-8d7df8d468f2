/* 商城页面设计 */
.container {
  padding: 30rpx 30rpx 120rpx; /* 增加底部内边距避免TabBar遮挡 */
}

/* 商品网格 */
.products-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.product-card {
  background-color: white;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.product-card:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.product-image {
  width: 100%;
  height: 280rpx;
  position: relative;
  overflow: hidden;
}

.product-image image {
  width: 100%;
  height: 100%;
}

.product-info {
  padding: 20rpx;
}

.product-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-description {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 16rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.product-price {
  font-size: 32rpx;
  font-weight: 600;
  color: #f44336;
}

/* 加载状态 - 参考抖音小程序设计 */
.loading, .loading-more, .no-more {
  text-align: center;
  padding: 30rpx 0;
  color: #757575;
  font-size: 28rpx;
}
