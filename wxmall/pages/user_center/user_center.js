const app = getApp()
const auth = require('../../utils/auth.js')
const api = require('../../utils/api.js')
const { imageManager } = require('../../utils/imageManager')

Page({
  data: {
    userInfo: null,
    orderStats: {
      pendingPayment: 0,
      pendingDelivery: 0,
      pendingReceipt: 0,
      completed: 0
    },
    repairStats: {
      pending: 0,
      accepted: 0,
      processing: 0,
      completed: 0,
      cancelled: 0,
      total: 0
    },
    unreadMessages: 0,
    menuIcons: {
      favorites: '',
      address: '',
      customerService: '',
      aboutUs: ''
    }
  },

  onLoad: function () {
    // 测试API模块是否正确加载

    if (api && api.CONFIG) {

    } else {

    }
  },

  onShow: function () {
    // 检查登录状态
    const isLoggedIn = this.checkLoginStatus();

    // 获取用户信息（每次onShow都刷新，确保从编辑页面返回后显示最新信息）
    this.getUserInfo();

    // 加载菜单图标
    this.loadMenuIcons();

    // 获取订单统计
    this.getOrderStats();

    // 根据登录状态获取维修订单统计
    if (isLoggedIn) {
      this.getRepairStats();
    } else {
      this.resetRepairStats();
    }

    // 设置TabBar选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 3
      })
    }
  },

  // 加载菜单图标
  loadMenuIcons: function() {
    imageManager.getIcons().then(icons => {
      const menuIcons = {
        favorites: this.findIconUrl(icons, '收藏') || imageManager.getDefaultImageUrl('icon'),
        address: this.findIconUrl(icons, '地址') || imageManager.getDefaultImageUrl('icon'),
        customerService: this.findIconUrl(icons, '客服') || imageManager.getDefaultImageUrl('icon'),
        aboutUs: this.findIconUrl(icons, '关于') || imageManager.getDefaultImageUrl('icon')
      };

      this.setData({
        menuIcons: menuIcons
      });
    }).catch(error => {

      // 使用默认图标
      const defaultIcon = imageManager.getDefaultImageUrl('icon');
      this.setData({
        menuIcons: {
          favorites: defaultIcon,
          address: defaultIcon,
          customerService: defaultIcon,
          aboutUs: defaultIcon
        }
      });
    });
  },

  // 查找图标URL
  findIconUrl: function(icons, keyword) {
    const icon = icons.find(item =>
      item.name.includes(keyword) ||
      item.name.toLowerCase().includes(keyword.toLowerCase())
    );
    return icon ? icon.url : '';
  },

  // 获取用户信息
  getUserInfo: function() {
    // 使用新的认证系统获取用户信息
    const loginState = auth.getLoginState();

    if (loginState.isLoggedIn && loginState.userInfo) {

      this.setData({
        userInfo: loginState.userInfo
      });

      // 调试：检查setData后的页面数据


    } else {

      this.setData({
        userInfo: null
      });
    }
  },

  // 获取订单统计
  getOrderStats: function() {
    // 实际开发中应该调用API获取订单统计数据
    // 这里使用模拟数据
    this.setData({
      orderStats: {
        pendingPayment: 0,
        pendingDelivery: 0,
        pendingReceipt: 0,
        completed: 0
      }
    });
  },

  // 获取维修订单统计
  getRepairStats: function() {
    const openId = app.globalData.openId;

    if (!openId) {
      // 尝试从本地存储中获取openId
      wx.getStorage({
        key: 'openId',
        success: (res) => {
          if (res.data) {
            this.fetchRepairStats(res.data);
          } else {
            // 如果本地存储也没有openId，重置统计数据为0
            this.resetRepairStats();
          }
        },
        fail: () => {
          // 获取本地存储失败，重置统计数据为0
          this.resetRepairStats();
        }
      });
      return;
    }

    this.fetchRepairStats(openId);
  },

  // 重置维修订单统计数据
  resetRepairStats: function() {
    this.setData({
      repairStats: {
        pending: 0,
        accepted: 0,
        processing: 0,
        completed: 0,
        cancelled: 0,
        total: 0
      }
    });
  },

  // 获取维修订单统计数据
  fetchRepairStats: function(openId) {
    api.getRepairOrderStats(openId).then(res => {
      if (res.success && res.stats) {
        // 确保所有字段都有默认值
        const stats = {
          pending: res.stats.pending || 0,
          accepted: res.stats.accepted || 0,
          processing: res.stats.processing || 0,
          completed: res.stats.completed || 0,
          cancelled: res.stats.cancelled || 0,
          total: res.stats.total || 0
        };

        this.setData({
          repairStats: stats
        });
      } else {
        this.resetRepairStats();
      }
    }).catch(err => {
      this.resetRepairStats();
    });
  },

  // 跳转到个人信息编辑页面
  goToProfileEdit: function() {
    wx.navigateTo({
      url: '/pages/profile_edit/profile_edit'
    });
  },

  // 完善用户信息（跳转到编辑页面）
  completeUserInfo: function() {
    // 检查是否已登录
    if (!this.checkLoginStatus()) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    // 跳转到个人信息编辑页面
    wx.navigateTo({
      url: '/pages/profile_edit/profile_edit'
    });
  },

  // 登录
  login: function() {
    const api = require('../../utils/api');

    // 直接在点击事件中获取用户信息
    wx.getUserProfile({
      desc: '用于完善会员资料',
      success: (userRes) => {
        // 获取登录code
        wx.login({
          success: (res) => {
            if (res.code) {
              // 发送 code 和用户信息到后端
              api.login(res.code, userRes.userInfo).then(loginRes => {
                if (loginRes.success) {

                  // 优先使用后端返回的用户信息（这是数据库中的真实信息）
                  const finalUserInfo = loginRes.userInfo || userRes.userInfo;
                  // 保存登录状态
                  const success = auth.saveLoginState(
                    loginRes.openId,
                    finalUserInfo,
                    loginRes.sessionToken
                  );

                  if (success) {
                    // 更新页面数据
                    this.setData({
                      userInfo: finalUserInfo
                    });

                    // 刷新统计数据
                    this.getRepairStats();

                    wx.showToast({
                      title: '登录成功',
                      icon: 'success'
                    });
                  }
                } else {
                  wx.showToast({
                    title: '登录失败',
                    icon: 'none'
                  });
                }
              }).catch(err => {
                wx.showToast({
                  title: '登录失败，请重试',
                  icon: 'none'
                });
              });
            }
          },
          fail: (err) => {
            wx.showToast({
              title: '登录失败，请重试',
              icon: 'none'
            });
          }
        });
      },
      fail: (err) => {
        wx.showModal({
          title: '提示',
          content: '需要您的授权才能正常使用小程序功能',
          showCancel: false
        });
      }
    });
  },

  // 退出登录
  logout: function() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          // 清除登录状态
          auth.clearLoginState();
          
          // 更新页面数据
          this.setData({
            userInfo: null
          });

          wx.showToast({
            title: '已退出登录',
            icon: 'success'
          });
        }
      }
    });
  },

  // 跳转到订单列表
  goToOrders: function(e) {
    const status = e.currentTarget.dataset.status;
    wx.navigateTo({
      url: `/pages/order_list/order_list?status=${status}`
    });
  },

  // 跳转到维修列表
  goToRepairList: function(e) {
    try {
      // 获取状态参数
      const status = e.currentTarget.dataset.status;

      // 构建跳转URL，所有状态都跳转到同一个repair_list页面
      let url = '/pages/repair_list/repair_list';
      if (status) {
        url += `?status=${status}`;
      }

      wx.navigateTo({
        url: url,
        success: () => {

        },
        fail: (err) => {

          this.showNavigationError();
        }
      });
    } catch (error) {

      this.showNavigationError();
    }
  },

  // 显示导航错误提示
  showNavigationError: function() {
    wx.showToast({
      title: '页面跳转失败，请重试',
      icon: 'none',
      duration: 2000
    });
  },

  // 跳转到收藏页面
  goToFavorites: function() {
    wx.navigateTo({
      url: '/pages/favorites/favorites'
    });
  },

  // 退出登录
  logout: function() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          // 清除登录状态
          app.globalData.openId = null;
          app.globalData.userInfo = null;

          // 清除本地存储
          wx.removeStorageSync('openId');
          wx.removeStorageSync('userInfo');

          // 更新页面数据
          this.setData({
            userInfo: null,
            isLoggedIn: false
          });

          // 重置维修订单统计数据
          this.resetRepairStats();

          wx.showToast({
            title: '已退出登录',
            icon: 'success'
          });
        }
      }
    });
  },

  // 检查登录状态
  checkLoginStatus: function() {
    try {
      const openId = app.globalData.openId || wx.getStorageSync('openId');

      return !!openId;
    } catch (error) {

      return false;
    }
  },

  // 跳转到地址管理
  goToAddressList: function() {
    wx.navigateTo({
      url: '/pages/address_list/address_list'
    });
  },

  // 跳转到客服
  goToCustomerService: function() {
    wx.navigateTo({
      url: '/pages/customer_service/customer_service'
    });
  },

  // 跳转到加入我们
  goToAboutUs: function() {
    wx.navigateTo({
      url: '/pages/join_us/join_us'
    });
  }
})
