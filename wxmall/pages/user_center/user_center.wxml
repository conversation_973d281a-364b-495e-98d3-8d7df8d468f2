<view class="container">
  <!-- 用户头部 -->
  <view class="user-header">
    <view class="user-info">
      <image src="{{userInfo.avatarUrl || '/images/default-avatar.png'}}" class="user-avatar" mode="aspectFill" />

      <view>
        <view class="user-name">{{userInfo.nickname || userInfo.nickName || '未登录'}}</view>

        <view class="login-btn" wx:if="{{!userInfo.nickName}}" bindtap="login">点击登录</view>
        <view class="edit-btn" wx:elif="{{userInfo.nickName && (userInfo.nickName === '用户' || userInfo.nickName === '微信用户' || userInfo.nickName.indexOf('用户') === 0 || !userInfo.avatarUrl)}}" bindtap="completeUserInfo">完善信息</view>
        <view class="edit-btn" wx:elif="{{userInfo.nickName}}" bindtap="goToProfileEdit">编辑资料</view>
      </view>
    </view>
    <view class="user-stats">
      <view class="stat-item" bindtap="goToOrders" data-status="pending_payment">
        <view class="stat-value">{{orderStats.pendingPayment || 0}}</view>
        <view class="stat-label">待付款</view>
      </view>
      <view class="stat-item" bindtap="goToOrders" data-status="pending_delivery">
        <view class="stat-value">{{orderStats.pendingDelivery || 0}}</view>
        <view class="stat-label">待发货</view>
      </view>
      <view class="stat-item" bindtap="goToOrders" data-status="pending_receipt">
        <view class="stat-value">{{orderStats.pendingReceipt || 0}}</view>
        <view class="stat-label">待收货</view>
      </view>
      <view class="stat-item" bindtap="goToOrders" data-status="completed">
        <view class="stat-value">{{orderStats.completed || 0}}</view>
        <view class="stat-label">已完成</view>
      </view>
    </view>
  </view>

  <!-- 维修服务 -->
  <view class="service-section">
    <view class="section-header">
      <view class="section-title">
        <text class="title-text">维修服务</text>
      </view>
      <view class="view-all" bindtap="goToRepairList">
        <text class="view-all-text">查看全部</text>
        <text class="arrow">></text>
      </view>
    </view>

    <view class="service-stats">
      <view class="stat-item" bindtap="goToRepairList" data-status="pending">
        <view class="stat-number-wrapper">
          <view class="stat-number">{{repairStats.pending}}</view>
        </view>
        <view class="stat-label">待接单</view>
      </view>

      <view class="stat-item" bindtap="goToRepairList" data-status="accepted">
        <view class="stat-number-wrapper">
          <view class="stat-number">{{repairStats.accepted}}</view>
        </view>
        <view class="stat-label">待上门</view>
      </view>

      <view class="stat-item" bindtap="goToRepairList" data-status="processing">
        <view class="stat-number-wrapper">
          <view class="stat-number">{{repairStats.processing}}</view>
        </view>
        <view class="stat-label">维修中</view>
      </view>

      <view class="stat-item" bindtap="goToRepairList" data-status="completed">
        <view class="stat-number-wrapper">
          <view class="stat-number">{{repairStats.completed}}</view>
        </view>
        <view class="stat-label">已完成</view>
      </view>
    </view>
  </view>

  <!-- 功能菜单 -->
  <view class="menu-group">
    <view class="menu-item" bindtap="goToFavorites">
      <view class="menu-icon">
        <image src="{{menuIcons.favorites}}" mode="aspectFit" style="width: 40rpx; height: 40rpx;"></image>
      </view>
      <view class="menu-title">我的收藏</view>
      <view class="menu-arrow">
        <text class="iconfont icon-right"></text>
      </view>
    </view>

    <view class="menu-item" bindtap="goToAddressList">
      <view class="menu-icon">
        <image src="{{menuIcons.address}}" mode="aspectFit" style="width: 40rpx; height: 40rpx;"></image>
      </view>
      <view class="menu-title">收货地址</view>
      <view class="menu-arrow">
        <text class="iconfont icon-right"></text>
      </view>
    </view>

    <view class="menu-item" bindtap="goToCustomerService">
      <view class="menu-icon">
        <image src="{{menuIcons.customerService}}" mode="aspectFit" style="width: 40rpx; height: 40rpx;"></image>
      </view>
      <view class="menu-title">客服中心</view>
      <view class="menu-arrow">
        <view class="badge-count" wx:if="{{unreadMessages > 0}}">{{unreadMessages}}</view>
        <text class="iconfont icon-right"></text>
      </view>
    </view>
  </view>

  <!-- 设置 -->
  <view class="menu-group">
    <view class="menu-item" bindtap="goToAboutUs">
      <view class="menu-icon">
        <image src="{{menuIcons.aboutUs}}" mode="aspectFit" style="width: 40rpx; height: 40rpx;"></image>
      </view>
      <view class="menu-title">加入我们</view>
      <view class="menu-arrow">
        <text class="iconfont icon-right"></text>
      </view>
    </view>
  </view>

  <!-- 退出登录按钮 - 放在最底部 -->
  <view wx:if="{{userInfo}}" class="logout-section">
    <view class="logout-button" bindtap="logout">
      <text class="logout-text">退出登录</text>
    </view>
  </view>

  <!-- 底部间距 -->
  <view class="bottom-spacing"></view>
</view>
