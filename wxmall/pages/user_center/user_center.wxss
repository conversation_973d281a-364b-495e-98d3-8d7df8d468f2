.container {
  padding: 30rpx 30rpx 0;
  min-height: 100vh;
  padding-bottom: 120rpx; /* 确保底部有足够空间 */
}

.user-header {
  background-color: #1e88e5;
  padding: 48rpx 32rpx;
  color: white;
  border-radius: 24rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.user-info {
  display: flex;
  align-items: center;
}

.user-avatar {
  width: 140rpx;
  height: 140rpx;
  border-radius: 50%;
  border: 4rpx solid white;
  margin-right: 32rpx;
}

.user-name {
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.login-btn, .edit-btn {
  font-size: 28rpx;
  background-color: rgba(255, 255, 255, 0.2);
  padding: 8rpx 24rpx;
  border-radius: 32rpx;
  display: inline-block;
  margin-top: 8rpx;
}

.edit-btn {
  background-color: rgba(76, 175, 80, 0.8);
}

.user-stats {
  display: flex;
  justify-content: space-around;
  margin-top: 32rpx;
  text-align: center;
}

.stat-item {
  flex: 1;
}

.stat-value {
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  opacity: 0.8;
}

.menu-group {
  background-color: white;
  border-radius: 24rpx;
  overflow: hidden;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1px solid #f0f0f0;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-icon {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 24rpx;
  color: #1e88e5;
  font-size: 40rpx;
}

.menu-title {
  flex: 1;
  font-size: 32rpx;
}

.menu-arrow {
  color: #757575;
  display: flex;
  align-items: center;
}

.menu-arrow .text-light {
  font-size: 28rpx;
  margin-right: 8rpx;
}

.iconfont {
  font-family: 'iconfont';
}

.icon-right:before {
  content: '>';
  font-size: 24rpx;
  color: #757575;
}

.badge-count {
  background-color: #f44336;
  color: white;
  border-radius: 20rpx;
  padding: 4rpx 12rpx;
  font-size: 24rpx;
  margin-right: 16rpx;
}

.repair-stats {
  display: flex;
  justify-content: space-around;
  padding: 24rpx 0;
  text-align: center;
}

.repair-stat-item {
  flex: 1;
}

.repair-stat-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #1e88e5;
  margin-bottom: 8rpx;
}

.repair-stat-label {
  font-size: 24rpx;
  color: #666;
}

/* 维修服务区域样式 */
.service-section {
  background-color: white;
  border-radius: 16rpx;
  margin: 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.section-title {
  display: flex;
  align-items: center;
}

.title-icon {
  font-size: 36rpx;
  margin-right: 12rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.view-all {
  display: flex;
  align-items: center;
  color: #1e88e5;
}

.view-all-text {
  font-size: 28rpx;
  margin-right: 8rpx;
}

.arrow {
  font-size: 24rpx;
  font-weight: bold;
}

.service-stats {
  display: flex;
  justify-content: space-between;
}

.stat-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 10rpx;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.stat-item:active {
  background-color: #f5f5f5;
  transform: scale(0.95);
}

.stat-number-wrapper {
  margin-bottom: 16rpx;
}

.stat-number {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  font-weight: 700;
  color: #1976d2;
  min-height: 60rpx;
}

.stat-label {
  font-size: 26rpx;
  color: #666;
  font-weight: 500;
}

/* 退出登录区域 */
.logout-section {
  margin-top: 40rpx;
  padding: 0 30rpx;
}

.logout-button {
  background: #f44336;
  border-radius: 12rpx;
  padding: 24rpx 0;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  margin-bottom: 20rpx;
}

.logout-button:active {
  background: #d32f2f;
  transform: scale(0.98);
}

.logout-text {
  color: white;
  font-size: 28rpx;
  font-weight: 500;
}

/* 底部间距 */
.bottom-spacing {
  height: 100rpx;
  background: transparent;
}
