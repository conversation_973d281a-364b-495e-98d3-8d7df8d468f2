.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 80px;
}

.status-bar {
  background-color: #1890ff;
  padding: 30px 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #fff;
}

.status-pending {
  background-color: #1890ff;
}

.status-accepted {
  background-color: #fa8c16;
}

.status-processing {
  background-color: #52c41a;
}

.status-completed {
  background-color: #8c8c8c;
}

.status-icon {
  margin-bottom: 10px;
}

.icon-wrapper {
  width: 60px;
  height: 60px;
  border-radius: 30px;
  background-color: rgba(255, 255, 255, 0.2);
  display: flex;
  justify-content: center;
  align-items: center;
}

.icon {
  width: 36px;
  height: 36px;
}

.status-text {
  font-size: 18px;
  font-weight: 500;
}

.card {
  background-color: #fff;
  border-radius: 8px;
  margin: 15px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.card-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.info-item {
  display: flex;
  margin-bottom: 12px;
}

.label {
  width: 80px;
  color: #999;
  font-size: 14px;
}

.value {
  flex: 1;
  color: #333;
  font-size: 14px;
}

.fee-total {
  color: #f5222d;
  font-weight: 500;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
}

.fault-image {
  width: 80px;
  height: 80px;
  border-radius: 4px;
  margin-right: 10px;
  margin-bottom: 10px;
}

.engineer {
  display: flex;
  align-items: center;
}

.engineer-avatar-container {
  position: relative;
  margin-right: 15px;
}

.engineer-avatar {
  width: 60px;
  height: 60px;
  border-radius: 30px;
}

.engineer-avatar-placeholder {
  width: 60px;
  height: 60px;
  border-radius: 30px;
  background-color: #1890ff;
  display: flex;
  justify-content: center;
  align-items: center;
}

.avatar-text {
  color: #fff;
  font-size: 20px;
  font-weight: 500;
}

.online-status {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 16px;
  height: 16px;
  border-radius: 8px;
  border: 2px solid #fff;
}

.online-status.online {
  background-color: #52c41a;
}

.online-status.offline {
  background-color: #d9d9d9;
}

.engineer-detail {
  flex: 1;
}

.engineer-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 5px;
}

.engineer-title {
  font-size: 14px;
  color: #999;
  margin-bottom: 5px;
}

.engineer-phone {
  font-size: 14px;
  color: #666;
  display: flex;
  align-items: center;
}

.engineer-contact {
  margin-left: 15px;
}

.contact-btn {
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  text-align: center;
}

.call-btn {
  background-color: #1890ff;
  color: #fff;
}

.progress-timeline {
  position: relative;
}

.timeline-item {
  display: flex;
  margin-bottom: 20px;
  position: relative;
}

.timeline-item:not(:last-child)::after {
  content: '';
  position: absolute;
  left: 7px;
  top: 20px;
  width: 2px;
  height: calc(100% + 5px);
  background-color: #e8e8e8;
}

.timeline-item.active:not(:last-child)::after {
  background-color: #1890ff;
}

.timeline-dot {
  width: 16px;
  height: 16px;
  border-radius: 8px;
  background-color: #e8e8e8;
  margin-right: 15px;
  margin-top: 2px;
  position: relative;
  z-index: 1;
}

.timeline-item.active .timeline-dot {
  background-color: #1890ff;
}

.timeline-content {
  flex: 1;
}

.timeline-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 5px;
}

.timeline-desc {
  font-size: 14px;
  color: #999;
}

.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 15px;
  border-top: 1px solid #eee;
  display: flex;
  gap: 15px;
}

.btn {
  flex: 1;
  height: 44px;
  border-radius: 22px;
  font-size: 16px;
  font-weight: 500;
  border: none;
  display: flex;
  justify-content: center;
  align-items: center;
}

.btn-default {
  background-color: #f5f5f5;
  color: #666;
}

.btn-primary {
  background-color: #1890ff;
  color: #fff;
}

.btn::after {
  border: none;
}
