const app = getApp()
const api = require('../../utils/api')

Page({
  data: {
    orderId: '',
    order: {},
    statusText: {
      'pending': '待接单',
      'accepted': '已接单',
      'processing': '维修中',
      'completed': '已完成',
      'cancelled': '已取消'
    },
    faultTypeMap: {
      'no_charging': '无法充电',
      'slow_charging': '充电慢',
      'error_code': '报错代码',
      'port_damage': '接口损坏',
      'not_starting': '无法启动',
      'overheating': '过热',
      'display_issue': '显示故障',
      'other': '其他故障'
    }
  },

  onLoad: function (options) {
    // 支持多种参数格式：id, orderNo, orderNumber
    const orderId = options.id || options.orderNo || options.orderNumber;
    if (!orderId) {
      wx.showToast({
        title: '订单ID不能为空',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }

    this.setData({
      orderId: orderId
    });

    this.loadOrderDetail();
  },

  // 加载订单详情
  loadOrderDetail: function() {
    wx.showLoading({
      title: '加载中...'
    });

    const openId = app.globalData.openId || wx.getStorageSync('openId');
    if (!openId) {
      wx.hideLoading();
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    api.getRepairOrderDetail(this.data.orderId, openId)
      .then(res => {
        wx.hideLoading();
        if (res.success && res.order) {
          const order = this.processOrderData(res.order);

          this.setData({
            order: order
          });
        } else {

          wx.showToast({
            title: res.message || '获取订单详情失败',
            icon: 'none'
          });
        }
      })
      .catch(err => {
        wx.hideLoading();

        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      });
  },

  // 处理订单数据
  processOrderData: function(orderData) {
    // 处理图片数组
    let images = [];
    if (orderData.images) {
      try {
        images = typeof orderData.images === 'string' 
          ? JSON.parse(orderData.images) 
          : orderData.images;
        
        // 处理图片URL
        images = images.map(img => this.processImageUrl(img));
      } catch (e) {

        images = [];
      }
    }

    // 处理工程师头像
    const processedEngineerAvatar = this.processAvatarUrl(orderData.engineerAvatar);

    // 格式化时间
    const createdAtFormatted = this.formatDateTime(orderData.createdAt);
    const completedAtFormatted = this.formatDateTime(orderData.completedAt);

    return {
      ...orderData,
      images: images,
      processedEngineerAvatar: processedEngineerAvatar,
      createdAtFormatted: createdAtFormatted,
      completedAtFormatted: completedAtFormatted
    };
  },

  // 处理图片URL
  processImageUrl: function(imageUrl) {
    if (!imageUrl || imageUrl.trim() === '') {
      return '';
    }

    // 如果已经是完整的HTTP/HTTPS URL，直接返回
    if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
      return imageUrl;
    }

    // 如果是相对路径，添加服务器地址
    if (imageUrl.startsWith('/uploads/')) {
      return `https://www.zhuanglz.cn:8443${imageUrl}`;
    }

    // 如果只是文件名，添加完整路径
    if (!imageUrl.startsWith('/')) {
      return `https://www.zhuanglz.cn:8443/uploads/${imageUrl}`;
    }

    // 其他情况，添加服务器地址
    return `https://www.zhuanglz.cn:8443${imageUrl}`;
  },

  // 处理头像URL
  processAvatarUrl: function(avatarUrl) {
    if (!avatarUrl || avatarUrl.trim() === '') {
      return '';
    }

    // 如果已经是完整的HTTP/HTTPS URL，直接返回
    if (avatarUrl.startsWith('http://') || avatarUrl.startsWith('https://')) {
      return avatarUrl;
    }

    // 如果是相对路径，添加服务器地址
    if (avatarUrl.startsWith('/uploads/')) {
      return `https://www.zhuanglz.cn:8443${avatarUrl}`;
    }

    // 如果只是文件名，添加完整路径
    if (!avatarUrl.startsWith('/')) {
      return `https://www.zhuanglz.cn:8443/uploads/${avatarUrl}`;
    }

    // 其他情况，添加服务器地址
    return `https://www.zhuanglz.cn:8443${avatarUrl}`;
  },

  // 格式化日期时间
  formatDateTime: function(dateTime) {
    if (!dateTime) return '';
    
    const date = new Date(dateTime);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    
    return `${year}-${month}-${day} ${hours}:${minutes}`;
  },

  // 获取维修结果文本
  getRepairResultText: function(result) {
    const resultMap = {
      'success': '维修成功',
      'partial': '部分修复',
      'failed': '维修失败',
      'replaced': '更换配件'
    };
    return resultMap[result] || '未知结果';
  },

  // 预览图片
  previewImage: function(e) {
    const url = e.currentTarget.dataset.url;
    wx.previewImage({
      current: url,
      urls: this.data.order.images || [url]
    });
  },

  // 拨打工程师电话
  callEngineer: function(e) {
    const phone = e.currentTarget.dataset.phone;
    if (!phone) {
      wx.showToast({
        title: '工程师电话不可用',
        icon: 'none'
      });
      return;
    }

    wx.makePhoneCall({
      phoneNumber: phone,
      success: () => {

      },
      fail: (err) => {

        wx.showToast({
          title: '拨打失败，请重试',
          icon: 'none'
        });
      }
    });
  },

  // 联系客服
  contactService: function() {
    wx.navigateTo({
      url: '/pages/customer_service/customer_service'
    });
  },

  // 取消订单
  cancelOrder: function() {
    wx.showModal({
      title: '确认取消',
      content: '确定要取消这个维修订单吗？',
      success: (res) => {
        if (res.confirm) {
          this.performCancelOrder();
        }
      }
    });
  },

  // 执行取消订单
  performCancelOrder: function() {
    wx.showLoading({
      title: '取消中...'
    });

    const openId = app.globalData.openId;
    api.cancelRepairOrder(this.data.orderId, openId)
      .then(res => {
        wx.hideLoading();
        
        if (res.success) {
          wx.showToast({
            title: '订单已取消',
            icon: 'success'
          });
          
          // 刷新订单详情
          this.loadOrderDetail();
        } else {
          wx.showToast({
            title: res.message || '取消失败',
            icon: 'none'
          });
        }
      })
      .catch(err => {
        wx.hideLoading();

        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      });
  },

  // 再次预约
  goToRepair: function() {
    wx.navigateTo({
      url: '/pages/repair_form/repair_form'
    });
  },

  // 工程师头像加载失败处理
  onEngineerAvatarError: function() {
    const order = this.data.order;
    order.processedEngineerAvatar = '';
    this.setData({
      order: order
    });
  }
})
