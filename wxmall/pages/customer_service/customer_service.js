// 严格按照抖音小程序customer_service模板设计
const app = getApp()

Page({
  data: {
    userInfo: null,
    currentTime: '',
    inputMessage: '',
    scrollToMessage: '',
    messages: [],
    sessionId: '',
    isConnected: false,
    isLoading: false,
    hasStartedChat: false,
    quickQuestions: [
      '充电桩无法充电怎么办？',
      '如何预约上门维修？',
      '维修费用是多少？',
      '有哪些常见故障？',
      '保修期是多久？'
    ]
  },

  // 消息轮询定时器
  messageTimer: null,

  onLoad: function () {
    // 获取当前时间
    this.updateCurrentTime();

    // 获取用户信息
    this.getUserInfo();

    // 初始化客服服务
    this.initCustomerService();
  },

  onShow: function () {
    // 页面显示时更新时间
    this.updateCurrentTime();

    // 页面显示时刷新消息
    if (this.data.sessionId) {
      this.loadMessages();
    }
  },

  onHide: function () {
    // 页面隐藏时清除定时器
    if (this.messageTimer) {
      clearInterval(this.messageTimer);
      this.messageTimer = null;
    }
  },

  onUnload: function () {
    // 页面卸载时清除定时器
    if (this.messageTimer) {
      clearInterval(this.messageTimer);
      this.messageTimer = null;
    }
  },

  // 更新当前时间
  updateCurrentTime: function() {
    const now = new Date();
    const hours = now.getHours().toString().padStart(2, '0');
    const minutes = now.getMinutes().toString().padStart(2, '0');

    this.setData({
      currentTime: `${hours}:${minutes}`
    });
  },

  // 获取用户信息
  getUserInfo: function() {
    const userInfo = app.globalData.userInfo;

    if (userInfo) {
      this.setData({
        userInfo: userInfo
      });
    } else {
      // 如果没有用户信息，尝试获取
      wx.getUserProfile({
        desc: '用于完善客服功能',
        success: (res) => {
          app.globalData.userInfo = res.userInfo;
          this.setData({
            userInfo: res.userInfo
          });
        }
      });
    }
  },

  // 初始化客服服务
  initCustomerService: function() {
    this.setData({ isLoading: true });

    const openId = wx.getStorageSync('openId');
    if (!openId) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }

    // 模拟连接成功
    setTimeout(() => {
      this.setData({
        sessionId: 'session_' + Date.now(),
        isConnected: true,
        isLoading: false
      });
      this.loadMessages();
    }, 1000);
  },

  // 加载消息历史
  loadMessages: function() {
    // 模拟加载消息
    const messages = [];

    this.setData({
      messages: messages,
      hasStartedChat: false
    });

    // 滚动到底部
    this.scrollToBottom();
  },

  // 输入框内容变化
  onInputChange: function(e) {
    this.setData({
      inputMessage: e.detail.value
    });
  },

  // 发送消息
  sendMessage: function() {
    const { inputMessage, messages } = this.data;

    if (!inputMessage.trim()) {
      return;
    }

    const newMessage = {
      id: Date.now(),
      type: 'user',
      content: inputMessage.trim(),
      time: this.getCurrentTime(),
      isTimeLabel: false
    };

    const updatedMessages = [...messages, newMessage];

    this.setData({
      messages: updatedMessages,
      inputMessage: '',
      hasStartedChat: true
    });

    // 模拟客服回复
    setTimeout(() => {
      this.autoReply(inputMessage.trim());
    }, 1000);

    this.scrollToBottom();
  },

  // 快捷问题发送
  sendQuickQuestion: function(e) {
    const question = e.currentTarget.dataset.question;
    this.setData({
      inputMessage: question
    });
    this.sendMessage();
  },

  // 长按消息显示时间
  onMessageLongPress: function(e) {
    const messageId = e.currentTarget.dataset.messageId;
    const messages = this.data.messages.map(msg => {
      if (msg.id === messageId) {
        return { ...msg, showDetailTime: !msg.showDetailTime };
      }
      return { ...msg, showDetailTime: false };
    });

    this.setData({ messages });
  },

  // 自动回复
  autoReply: function(userMessage) {
    let replyContent = '';

    if (userMessage.includes('维修') || userMessage.includes('故障')) {
      replyContent = '我们提供专业的充电桩维修服务，包括上门维修、远程指导和网点维修。您可以在维修页面提交故障报修，我们会安排专业工程师为您服务。';
    } else if (userMessage.includes('价格') || userMessage.includes('费用')) {
      replyContent = '我们的维修费用根据故障类型和服务方式而定，具体价格请在提交维修申请后，工程师会为您提供详细报价。';
    } else if (userMessage.includes('时间') || userMessage.includes('多久')) {
      replyContent = '一般情况下，我们会在2小时内响应您的维修申请，上门维修通常在24小时内完成。具体时间会根据故障复杂程度和您的位置而定。';
    } else if (userMessage.includes('商品') || userMessage.includes('配件')) {
      replyContent = '我们提供各种充电桩配件和维修用品，您可以在商城页面浏览购买。所有商品均为正品，享受质量保证。';
    } else {
      replyContent = '感谢您的咨询！如果您有具体的问题，请详细描述，我会尽力为您解答。您也可以拨打客服热线：************';
    }

    const replyMessage = {
      id: Date.now(),
      type: 'service',
      content: replyContent,
      time: this.getCurrentTime(),
      isTimeLabel: false
    };

    const updatedMessages = [...this.data.messages, replyMessage];
    
    this.setData({
      messages: updatedMessages
    });

    this.scrollToBottom();
  },

  // 获取当前时间
  getCurrentTime: function() {
    const now = new Date();
    const hours = now.getHours().toString().padStart(2, '0');
    const minutes = now.getMinutes().toString().padStart(2, '0');
    return `${hours}:${minutes}`;
  },

  // 滚动到底部
  scrollToBottom: function() {
    if (this.data.messages.length > 0) {
      const lastMessage = this.data.messages[this.data.messages.length - 1];
      this.setData({
        scrollToMessage: `msg-${lastMessage.id}`
      });
    }
  },

  // 选择图片
  chooseImage: function() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePath = res.tempFilePaths[0];

        // 这里可以上传图片到服务器
        wx.showToast({
          title: '图片发送功能开发中',
          icon: 'none'
        });
      }
    });
  },

  // 拨打电话
  makePhoneCall: function() {
    wx.makePhoneCall({
      phoneNumber: '************',
      fail: () => {
        wx.showToast({
          title: '拨号失败',
          icon: 'none'
        });
      }
    });
  },

  // 页面分享
  onShareAppMessage: function () {
    return {
      title: '桩郎中客服 - 专业充电桩维修服务',
      path: '/pages/customer_service/customer_service',
      imageUrl: '/images/share/customer_service.png'
    };
  },

  // 页面分享到朋友圈
  onShareTimeline: function () {
    return {
      title: '桩郎中客服 - 专业充电桩维修服务',
      imageUrl: '/images/share/customer_service.png'
    };
  }
})
