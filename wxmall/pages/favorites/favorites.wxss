.container {
  padding: 30rpx;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.page-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.clear-btn {
  font-size: 28rpx;
  color: #1e88e5;
  padding: 8rpx 16rpx;
}

.favorites-list {
  background-color: white;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.favorite-item {
  display: flex;
  padding: 32rpx;
  border-bottom: 1px solid #f0f0f0;
  align-items: center;
}

.favorite-item:last-child {
  border-bottom: none;
}

.product-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 16rpx;
  overflow: hidden;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.product-image image {
  width: 100%;
  height: 100%;
}

.product-info {
  flex: 1;
  margin-right: 24rpx;
}

.product-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-description {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-price {
  font-size: 36rpx;
  font-weight: 600;
  color: #f44336;
}

.product-actions {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  flex-shrink: 0;
}

.action-btn {
  padding: 12rpx 24rpx;
  border-radius: 16rpx;
  font-size: 24rpx;
  border: none;
  min-width: 120rpx;
}

.remove-btn {
  background-color: #f5f5f5;
  color: #666;
}

.cart-btn {
  background-color: #1e88e5;
  color: white;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
}

.empty-title {
  font-size: 36rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 16rpx;
}

.empty-description {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 48rpx;
}

.go-shopping-btn {
  background-color: #1e88e5;
  color: white;
  border: none;
  border-radius: 32rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
}
