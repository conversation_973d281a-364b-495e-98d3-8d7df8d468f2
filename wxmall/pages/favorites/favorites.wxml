<view class="container">
  <!-- 页面标题 -->
  <view class="page-header">
    <view class="page-title">我的收藏</view>
    <view class="clear-btn" wx:if="{{favorites.length > 0}}" bindtap="clearAllFavorites">
      清空
    </view>
  </view>

  <!-- 收藏列表 -->
  <view class="favorites-list" wx:if="{{!loading && !isEmpty}}">
    <view 
      class="favorite-item" 
      wx:for="{{favorites}}" 
      wx:key="id"
      bindtap="goToProductDetail" 
      data-id="{{item.productId || item.id}}"
    >
      <view class="product-image">
        <image src="{{item.image}}" mode="aspectFill" />
      </view>
      
      <view class="product-info">
        <view class="product-name">{{item.name || item.productName}}</view>
        <view class="product-description">{{item.description || item.shortDescription}}</view>
        <view class="product-price">¥{{item.price}}</view>
      </view>
      
      <view class="product-actions">
        <button 
          class="action-btn remove-btn" 
          bindtap="removeFavorite" 
          data-id="{{item.productId || item.id}}"
          data-index="{{index}}"
          catchtap="true"
        >
          取消收藏
        </button>
        <button 
          class="action-btn cart-btn" 
          bindtap="addToCart" 
          data-index="{{index}}"
          catchtap="true"
        >
          加购物车
        </button>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 空状态 -->
  <view class="empty-container" wx:if="{{!loading && isEmpty}}">
    <view class="empty-icon">💝</view>
    <view class="empty-title">暂无收藏商品</view>
    <view class="empty-description">快去收藏你喜欢的商品吧</view>
    <button class="go-shopping-btn" bindtap="goToMall">去逛逛</button>
  </view>
</view>
