const app = getApp()
const api = require('../../utils/api');
const auth = require('../../utils/auth');

Page({
  data: {
    favorites: [],
    loading: true,
    isEmpty: false
  },

  onLoad: function () {
    this.checkLoginAndLoad();
  },

  onShow: function () {
    this.checkLoginAndLoad();
  },

  onPullDownRefresh: function () {
    this.loadFavorites();
  },

  // 检查登录状态并加载数据
  checkLoginAndLoad: function() {
    const loginState = auth.getLoginState();
    
    if (!loginState.isLoggedIn) {
      wx.showModal({
        title: '请先登录',
        content: '登录后才能查看收藏',
        confirmText: '去登录',
        cancelText: '返回',
        success: (res) => {
          if (res.confirm) {
            // 触发登录
            app.login && app.login();
          } else {
            wx.navigateBack();
          }
        }
      });
      return;
    }

    this.loadFavorites();
  },

  // 加载收藏列表
  loadFavorites: function() {
    this.setData({ loading: true });

    api.getFavorites().then(res => {
      if (res.success && res.data) {
        const favorites = res.data.map(item => ({
          ...item,
          image: this.processImageUrl(item.image || item.mainImage)
        }));

        this.setData({
          favorites: favorites,
          loading: false,
          isEmpty: favorites.length === 0
        });
      } else {
        this.setData({
          favorites: [],
          loading: false,
          isEmpty: true
        });
      }
    }).catch(err => {
      console.error('加载收藏列表失败:', err);
      this.setData({
        favorites: [],
        loading: false,
        isEmpty: true
      });
    }).finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 处理图片URL
  processImageUrl: function(imageUrl) {
    if (!imageUrl || imageUrl.trim() === '') {
      return '/images/products/充电线缆.png';
    }

    if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
      return imageUrl;
    }

    if (imageUrl.startsWith('/uploads/')) {
      return `https://www.zhuanglz.cn:8443${imageUrl}`;
    }

    if (!imageUrl.startsWith('/')) {
      return `https://www.zhuanglz.cn:8443/uploads/${imageUrl}`;
    }

    return `https://www.zhuanglz.cn:8443${imageUrl}`;
  },

  // 跳转到商品详情
  goToProductDetail: function(e) {
    const productId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/product_detail/product_detail?id=${productId}`
    });
  },

  // 取消收藏
  removeFavorite: function(e) {
    const productId = e.currentTarget.dataset.id;
    const index = e.currentTarget.dataset.index;

    wx.showModal({
      title: '确认取消收藏',
      content: '确定要取消收藏这个商品吗？',
      success: (res) => {
        if (res.confirm) {
          api.removeFromFavorites(productId).then(result => {
            if (result.success) {
              // 从列表中移除
              const favorites = this.data.favorites;
              favorites.splice(index, 1);
              
              this.setData({
                favorites: favorites,
                isEmpty: favorites.length === 0
              });

              wx.showToast({
                title: '已取消收藏',
                icon: 'success'
              });
            } else {
              wx.showToast({
                title: '操作失败，请重试',
                icon: 'none'
              });
            }
          }).catch(err => {
            console.error('取消收藏失败:', err);
            wx.showToast({
              title: '操作失败，请重试',
              icon: 'none'
            });
          });
        }
      }
    });
  },

  // 添加到购物车
  addToCart: function(e) {
    const index = e.currentTarget.dataset.index;
    const product = this.data.favorites[index];
    
    if (product) {
      app.addToCart(product, 1);
      wx.showToast({
        title: '已添加到购物车',
        icon: 'success'
      });
    }
  },

  // 清空收藏
  clearAllFavorites: function() {
    if (this.data.favorites.length === 0) {
      return;
    }

    wx.showModal({
      title: '确认清空',
      content: '确定要清空所有收藏吗？此操作不可恢复。',
      success: (res) => {
        if (res.confirm) {
          // 这里应该调用批量删除API，暂时用循环删除
          const promises = this.data.favorites.map(item => 
            api.removeFromFavorites(item.productId || item.id)
          );

          Promise.all(promises).then(() => {
            this.setData({
              favorites: [],
              isEmpty: true
            });

            wx.showToast({
              title: '已清空收藏',
              icon: 'success'
            });
          }).catch(err => {
            console.error('清空收藏失败:', err);
            wx.showToast({
              title: '操作失败，请重试',
              icon: 'none'
            });
          });
        }
      }
    });
  },

  // 去购物
  goToMall: function() {
    wx.switchTab({
      url: '/pages/mall/mall'
    });
  }
})
