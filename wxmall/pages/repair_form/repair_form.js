// 严格按照抖音小程序repair_form模板设计
const app = getApp()

Page({
  data: {
    currentStep: 1,
    faultTypeIndex: -1,
    faultTypes: [
      { id: 1, name: '无法充电', type: 'no_charging' },
      { id: 2, name: '充电慢', type: 'slow_charging' },
      { id: 3, name: '报错代码', type: 'error_code' },
      { id: 4, name: '接口损坏', type: 'port_damage' },
      { id: 5, name: '无法启动', type: 'not_starting' },
      { id: 6, name: '过热', type: 'overheating' },
      { id: 7, name: '显示故障', type: 'display_issue' },
      { id: 8, name: '其他故障', type: 'other' }
    ],
    formData: {
      faultType: '',
      model: '',
      description: '',
      name: '',
      phone: '',
      addressId: '',
      fullAddress: '',
      serviceType: 'home', // 默认上门维修
      images: []
    }
  },

  onLoad: function (options) {

    // 如果有故障类型参数，设置默认值
    if (options.type) {
      const faultType = options.type;
      const index = this.data.faultTypes.findIndex(item => item.type === faultType);

      if (index !== -1) {
        this.setData({
          faultTypeIndex: index,
          'formData.faultType': this.data.faultTypes[index].type
        });
      }
    }

    // 获取用户信息
    this.getUserInfo();
  },

  onShow: function() {
    // 检查是否从地址选择页面返回
    const selectedAddress = app.globalData.selectedAddress;
    if (selectedAddress) {
      this.setAddressFromList(selectedAddress);

      // 清除全局数据
      app.globalData.selectedAddress = null;
    }
  },

  // 获取用户信息
  getUserInfo: function() {
    const userInfo = app.globalData.userInfo;

    if (userInfo) {
      this.setData({
        'formData.name': userInfo.nickName || ''
      });
    }
  },

  // 故障类型选择
  onFaultTypeChange: function(e) {
    const index = e.detail.value;

    this.setData({
      faultTypeIndex: index,
      'formData.faultType': this.data.faultTypes[index].type
    });
  },

  // 输入框事件
  onModelInput: function(e) {
    this.setData({
      'formData.model': e.detail.value
    });
  },

  onDescriptionInput: function(e) {
    this.setData({
      'formData.description': e.detail.value
    });
  },

  onNameInput: function(e) {
    this.setData({
      'formData.name': e.detail.value
    });
  },

  onPhoneInput: function(e) {
    this.setData({
      'formData.phone': e.detail.value
    });
  },

  onAddressInput: function(e) {
    this.setData({
      'formData.fullAddress': e.detail.value
    });
  },

  // 服务类型选择 - 按照抖音小程序模板
  selectServiceType: function(e) {
    const serviceType = e.currentTarget.dataset.type;
    this.setData({
      'formData.serviceType': serviceType
    });
  },

  // 跳转到地址列表页面
  goToAddressList: function() {
    wx.navigateTo({
      url: '/pages/address_list/address_list?from=repair_form'
    });
  },

  // 从地址列表页面返回时设置地址
  setAddressFromList: function(address) {
    if (!address) return;

    // 组合完整地址
    const fullAddress = address.province + address.city + address.district + address.address;

    this.setData({
      'formData.addressId': address.id,
      'formData.name': address.name,
      'formData.phone': address.phone,
      'formData.fullAddress': fullAddress
    });
  },

  // 选择图片
  chooseImage: function() {
    wx.chooseImage({
      count: 3 - this.data.formData.images.length,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        // 合并已有图片和新选择的图片
        const images = this.data.formData.images.concat(res.tempFilePaths);

        this.setData({
          'formData.images': images
        });
      }
    });
  },

  // 删除图片
  deleteImage: function(e) {
    const index = e.currentTarget.dataset.index;
    const images = this.data.formData.images;

    images.splice(index, 1);

    this.setData({
      'formData.images': images
    });
  },

  // 预览图片
  previewImage: function(e) {
    const url = e.currentTarget.dataset.url;

    wx.previewImage({
      current: url,
      urls: this.data.formData.images
    });
  },

  // 选择图片
  chooseImage: function() {
    const currentImages = this.data.formData.images;
    const maxCount = 3 - currentImages.length;

    if (maxCount <= 0) {
      wx.showToast({
        title: '最多只能上传3张图片',
        icon: 'none'
      });
      return;
    }

    wx.chooseImage({
      count: maxCount,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const newImages = [...currentImages, ...res.tempFilePaths];
        this.setData({
          'formData.images': newImages
        });
      },
      fail: (err) => {
        console.error('选择图片失败:', err);
        wx.showToast({
          title: '选择图片失败',
          icon: 'none'
        });
      }
    });
  },

  // 删除图片
  deleteImage: function(e) {
    const index = e.currentTarget.dataset.index;
    const images = this.data.formData.images;
    images.splice(index, 1);
    
    this.setData({
      'formData.images': images
    });
  },

  // 预览图片
  previewImage: function(e) {
    const url = e.currentTarget.dataset.url;
    const images = this.data.formData.images;
    
    wx.previewImage({
      current: url,
      urls: images
    });
  },

  // 表单验证
  validateForm: function() {
    const formData = this.data.formData;

    if (!formData.faultType) {
      wx.showToast({
        title: '请选择故障类型',
        icon: 'none'
      });
      return false;
    }

    if (!formData.model) {
      wx.showToast({
        title: '请输入充电桩型号',
        icon: 'none'
      });
      return false;
    }

    if (!formData.description) {
      wx.showToast({
        title: '请描述故障情况',
        icon: 'none'
      });
      return false;
    }

    if (!formData.name) {
      wx.showToast({
        title: '请输入联系人姓名',
        icon: 'none'
      });
      return false;
    }

    if (!formData.phone) {
      wx.showToast({
        title: '请输入联系电话',
        icon: 'none'
      });
      return false;
    }

    // 简单的手机号验证
    if (!/^1\d{10}$/.test(formData.phone)) {
      wx.showToast({
        title: '请输入正确的手机号',
        icon: 'none'
      });
      return false;
    }

    if (formData.serviceType === 'home' && !formData.addressId) {
      wx.showToast({
        title: '请选择维修地址',
        icon: 'none'
      });
      return false;
    }

    return true;
  },

  // 下一步
  nextStep: function() {
    // 表单验证
    if (!this.validateForm()) {
      return;
    }

    // 保存表单数据到全局
    app.globalData.repairInfo = this.data.formData;

    // 跳转到选择时间页面，使用redirectTo替代当前页面
    wx.redirectTo({
      url: '/pages/repair_time/repair_time',
      success: () => {
      },
      fail: (err) => {
      }
    });
  }
})
