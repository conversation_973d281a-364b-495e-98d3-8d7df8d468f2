/**
 * 图片资源管理工具类
 * 用于管理小程序中的图片资源，支持从后端动态获取
 */

const api = require('./api');

class ImageManager {
  constructor() {
    this.cache = new Map(); // 图片缓存
    this.loading = new Map(); // 加载状态缓存
  }

  /**
   * 获取轮播图
   * @param {string} pageType 页面类型，如 'home', 'repair'
   * @returns {Promise<Array>} 轮播图列表
   */
  async getBanners(pageType = null) {
    const cacheKey = `banners_${pageType || 'all'}`;

    // 检查缓存
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey);
    }

    // 检查是否正在加载
    if (this.loading.has(cacheKey)) {
      return this.loading.get(cacheKey);
    }

    // 开始加载
    const loadingPromise = this._loadBanners(pageType);
    this.loading.set(cacheKey, loadingPromise);

    try {
      const result = await loadingPromise;
      this.cache.set(cacheKey, result);
      this.loading.delete(cacheKey);
      return result;
    } catch (error) {
      this.loading.delete(cacheKey);
      throw error;
    }
  }

  /**
   * 获取图标
   * @returns {Promise<Array>} 图标列表
   */
  async getIcons() {
    const cacheKey = 'icons';

    // 检查缓存
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey);
    }

    // 检查是否正在加载
    if (this.loading.has(cacheKey)) {
      return this.loading.get(cacheKey);
    }

    // 开始加载
    const loadingPromise = this._loadIcons();
    this.loading.set(cacheKey, loadingPromise);

    try {
      const result = await loadingPromise;
      this.cache.set(cacheKey, result);
      this.loading.delete(cacheKey);
      return result;
    } catch (error) {
      this.loading.delete(cacheKey);
      throw error;
    }
  }

  /**
   * 根据名称获取图标URL
   * @param {string} iconName 图标名称
   * @returns {Promise<string>} 图标URL
   */
  async getIconUrl(iconName) {
    try {
      const icons = await this.getIcons();
      const icon = icons.find(item =>
        item.name === iconName ||
        item.name.includes(iconName) ||
        item.url.includes(iconName)
      );
      return icon ? icon.url : '';
    } catch (error) {

      return '';
    }
  }

  /**
   * 根据名称获取轮播图URL
   * @param {string} bannerName 轮播图名称
   * @param {string} pageType 页面类型
   * @returns {Promise<string>} 轮播图URL
   */
  async getBannerUrl(bannerName, pageType = null) {
    try {
      const banners = await this.getBanners(pageType);
      const banner = banners.find(item =>
        item.name === bannerName ||
        item.name.includes(bannerName) ||
        item.url.includes(bannerName)
      );
      return banner ? banner.url : '';
    } catch (error) {

      return '';
    }
  }

  /**
   * 获取所有图片资源
   * @returns {Promise<Object>} 所有图片资源，按分类分组
   */
  async getAllImages() {
    const cacheKey = 'all_images';
    
    // 检查缓存
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey);
    }

    try {
      const response = await api.getAllImages();
      if (response.success) {
        this.cache.set(cacheKey, response.data);
        return response.data;
      } else {
        throw new Error(response.message || '获取图片资源失败');
      }
    } catch (error) {

      return { banners: {}, icons: {} };
    }
  }

  /**
   * 清除缓存
   * @param {string} category 可选，指定清除的分类
   */
  clearCache(category = null) {
    if (category) {
      // 清除指定分类的缓存
      for (const key of this.cache.keys()) {
        if (key.startsWith(category)) {
          this.cache.delete(key);
        }
      }
    } else {
      // 清除所有缓存
      this.cache.clear();
    }
  }

  /**
   * 预加载图片资源
   * @param {Array} categories 要预加载的分类
   */
  async preloadImages(categories = ['banners', 'icons']) {
    const promises = [];

    if (categories.includes('banners')) {
      promises.push(this.getBanners('home'));
      promises.push(this.getBanners('repair'));
    }

    if (categories.includes('icons')) {
      promises.push(this.getIcons());
    }

    try {
      await Promise.all(promises);

    } catch (error) {

    }
  }

  /**
   * 私有方法：加载轮播图
   */
  async _loadBanners(pageType) {
    try {
      const response = await api.getBanners(pageType);
      if (response.success) {
        return response.data || [];
      } else {
        throw new Error(response.message || '获取轮播图失败');
      }
    } catch (error) {

      return [];
    }
  }

  /**
   * 私有方法：加载图标
   */
  async _loadIcons() {
    try {
      const response = await api.getIcons();
      if (response.success) {
        return response.data || [];
      } else {
        throw new Error(response.message || '获取图标失败');
      }
    } catch (error) {

      return [];
    }
  }

  /**
   * 获取默认图片URL（降级处理）
   * @param {string} type 图片类型：'banner', 'icon', 'avatar', 'product'
   * @returns {string} 默认图片URL
   */
  getDefaultImageUrl(type = 'default') {
    const defaultImages = {
      banner: '/images/default/banner.jpg',
      icon: '/images/default/icon.png',
      avatar: '/images/default/avatar.png',
      product: '/images/default/product.jpg',
      default: '/images/default/placeholder.png'
    };
    
    return defaultImages[type] || defaultImages.default;
  }

  /**
   * 图片加载错误处理
   * @param {string} originalUrl 原始图片URL
   * @param {string} fallbackType 降级图片类型
   * @returns {string} 降级图片URL
   */
  handleImageError(originalUrl, fallbackType = 'default') {

    return this.getDefaultImageUrl(fallbackType);
  }
}

// 创建单例实例
const imageManager = new ImageManager();

// 导出实例和类
module.exports = {
  imageManager,
  ImageManager
};
