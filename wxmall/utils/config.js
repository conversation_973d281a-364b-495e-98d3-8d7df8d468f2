/**
 * 应用配置文件 - 微信小程序版本
 * 统一管理API地址、常量等配置信息
 */

// 环境配置
const ENV = {
  // 开发环境
  development: {
    API_BASE_URL: 'https://www.zhuanglz.cn:8443',
    WS_BASE_URL: 'wss://www.zhuanglz.cn:8443',
    DEBUG: true
  },
  // 生产环境
  production: {
    API_BASE_URL: 'https://www.zhuanglz.cn',
    WS_BASE_URL: 'wss://www.zhuanglz.cn',
    DEBUG: false
  }
};

// 当前环境（开发时使用development，上线时改为production）
const CURRENT_ENV = 'development';

// 导出当前环境配置
const CONFIG = ENV[CURRENT_ENV];

// API接口地址
const API = {
  // 基础地址
  BASE_URL: CONFIG.API_BASE_URL,
  
  // 用户相关
  USER: {
    LOGIN: `${CONFIG.API_BASE_URL}/api/user/login`,
    PROFILE: `${CONFIG.API_BASE_URL}/api/user/profile`,
    UPDATE: `${CONFIG.API_BASE_URL}/api/user/update`
  },
  
  // 客服相关
  CUSTOMER_SERVICE: {
    SESSION: `${CONFIG.API_BASE_URL}/api/customer-service/session`,
    MESSAGES: `${CONFIG.API_BASE_URL}/api/customer-service/messages`,
    SEND_MESSAGE: `${CONFIG.API_BASE_URL}/api/customer-service/message`,
    MARK_READ: `${CONFIG.API_BASE_URL}/api/customer-service/read`
  },
  
  // 维修相关
  REPAIR: {
    CREATE: `${CONFIG.API_BASE_URL}/api/repair/create`,
    LIST: `${CONFIG.API_BASE_URL}/api/repair/list`,
    DETAIL: `${CONFIG.API_BASE_URL}/api/repair/detail`,
    CANCEL: `${CONFIG.API_BASE_URL}/api/repair/cancel`
  },
  
  // 商品相关
  PRODUCT: {
    LIST: `${CONFIG.API_BASE_URL}/api/products/list`,
    DETAIL: `${CONFIG.API_BASE_URL}/api/products/detail`,
    CATEGORIES: `${CONFIG.API_BASE_URL}/api/products/categories`
  },
  
  // 收藏相关
  FAVORITE: {
    LIST: `${CONFIG.API_BASE_URL}/api/favorites`,
    ADD: `${CONFIG.API_BASE_URL}/api/favorites`,
    REMOVE: `${CONFIG.API_BASE_URL}/api/favorites`
  },
  
  // 文件上传
  UPLOAD: {
    IMAGE: `${CONFIG.API_BASE_URL}/api/upload/image`,
    FILE: `${CONFIG.API_BASE_URL}/api/upload/file`
  }
};

// WebSocket配置
const WS = {
  BASE_URL: CONFIG.WS_BASE_URL,
  CUSTOMER_SERVICE: `${CONFIG.WS_BASE_URL}/ws/customer-service`
};

// 常量配置
const CONSTANTS = {
  // 分页配置
  PAGE_SIZE: 10,
  
  // 图片配置
  MAX_IMAGE_SIZE: 5 * 1024 * 1024, // 5MB
  ALLOWED_IMAGE_TYPES: ['jpg', 'jpeg', 'png', 'gif'],
  
  // 缓存配置
  CACHE_EXPIRE_TIME: 30 * 60 * 1000, // 30分钟
  
  // 订单状态
  ORDER_STATUS: {
    PENDING: 'pending',
    ACCEPTED: 'accepted',
    PROCESSING: 'processing',
    COMPLETED: 'completed',
    CANCELLED: 'cancelled'
  }
};

// 工具函数
const UTILS = {
  /**
   * 构建完整的API URL
   */
  buildApiUrl: (path) => {
    return `${CONFIG.API_BASE_URL}${path}`;
  },
  
  /**
   * 获取图片完整URL
   */
  getImageUrl: (imagePath) => {
    if (!imagePath) return '';
    if (imagePath.startsWith('http')) return imagePath;
    return `${CONFIG.API_BASE_URL}${imagePath}`;
  },
  
  /**
   * 检查是否为开发环境
   */
  isDevelopment: () => {
    return CURRENT_ENV === 'development';
  },
  
  /**
   * 调试日志（仅在开发环境输出）
   */
  debugLog: (message, data) => {
    if (CONFIG.DEBUG) {

    }
  }
};

module.exports = {
  CONFIG,
  API,
  WS,
  CONSTANTS,
  UTILS
};
