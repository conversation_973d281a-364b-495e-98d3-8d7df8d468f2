/**
 * API工具 - 微信小程序版本
 * 用于与后端API进行通信
 */

// 配置信息
const CONFIG = {
  API_BASE_URL: 'https://www.zhuanglz.cn:8443/api',  // 使用HTTPS协议，包含/api前缀，与抖音小程序保持一致
  SERVER_BASE_URL: 'https://www.zhuanglz.cn:8443',   // 服务器基础地址（用于图片等资源）
  DEBUG: true
};

const API_CONFIG = {
  BASE_URL: CONFIG.API_BASE_URL
};

/**
 * 微信小程序API包装器
 * 将抖音小程序的tt.request替换为wx.request
 */

// 请求方法 - 使用微信小程序API
const request = (url, method, data) => {
  return new Promise((resolve, reject) => {
    // 构建完整URL
    const fullUrl = url.startsWith('http') ? url : `${API_CONFIG.BASE_URL}${url}`;

    // 调试日志
    if (CONFIG.DEBUG) {
      console.log('🌐 API Request:', method, fullUrl, data);
    }
    
    wx.request({
      url: fullUrl,
      method: method,
      data: data,
      header: {
        'content-type': 'application/json'
      },
      success: (res) => {
        if (CONFIG.DEBUG) {
          console.log('✅ API Response:', res.statusCode, res.data);
        }
        if (res.statusCode === 200) {
          resolve(res.data);
        } else {
          reject(res.data || { message: '请求失败' });
        }
      },
      fail: (err) => {
        if (CONFIG.DEBUG) {
          console.error('❌ API Error:', err);
        }
        reject(err);
      }
    });
  });
};

// GET请求
const get = (url, data) => {
  return request(url, 'GET', data);
};

// POST请求
const post = (url, data) => {
  return request(url, 'POST', data);
};

// PUT请求
const put = (url, data) => {
  return request(url, 'PUT', data);
};

// DELETE请求
const del = (url, data) => {
  return request(url, 'DELETE', data);
};

/**
 * 用户认证相关API
 */

// 微信登录code2Session
const code2Session = (code) => {
  return post('/auth/wx/code2session', { code });
};

// 微信完整登录
const login = (code, userInfo) => {
  return post('/auth/wx/login', { code, userInfo });
};

// 通过openId获取用户信息
const getUserInfoByOpenId = (openId) => {
  return get(`/auth/wx/getUserInfo?openId=${openId}`);
};

// 更新用户信息
const updateUserInfo = (openId, userInfo) => {
  return post('/auth/wx/updateUserInfo', { openId, userInfo });
};

// 退出登录
const logout = () => {
  return post('/auth/wx/logout');
};

/**
 * 地址相关API - 与抖音小程序保持一致
 */

// 获取用户地址列表
const getAddressList = (openId) => {
  return get(`/address/list?openId=${openId}`);
};

// 获取用户默认地址
const getDefaultAddress = (openId) => {
  return get(`/address/default?openId=${openId}`);
};

// 保存地址（新增或修改）
const saveAddress = (addressData) => {
  return post('/address/save', addressData);
};

// 删除地址
const deleteAddress = (id, openId) => {
  return request(`/address/${id}?openId=${openId}`, 'DELETE');
};

// 设置默认地址
const setDefaultAddress = (id, openId) => {
  return post(`/address/${id}/default?openId=${openId}`);
};

/**
 * 商品相关API - 与抖音小程序保持一致
 */

// 获取商品分类列表
const getProductCategories = () => {
  return get('/products/categories');
};

// 获取商品列表
const getProductList = () => {
  return get('/products/list');
};

// 获取热门产品（按sort_order排序，取前几个）
const getHotProducts = (limit = 2) => {
  return get(`/products/list?sortType=sortOrder&limit=${limit}`);
};

// 获取商品详情 - 与抖音小程序API路径一致
const getProductDetail = (id) => {
  return get(`/products/detail/${id}`);
};

// 获取商品评价列表
const getProductReviews = (id) => {
  return get(`/products/${id}/reviews`);
};

// 搜索商品
const searchProducts = (keyword, params = {}) => {
  return get('/products/search', { keyword, ...params });
};

/**
 * 购物车相关API
 */

// 添加到购物车
const addToCart = (productId, quantity = 1) => {
  return post('/cart/add', { productId, quantity });
};

// 获取购物车
const getCart = () => {
  return get('/cart');
};

// 更新购物车商品数量
const updateCartItem = (itemId, quantity) => {
  return put(`/cart/items/${itemId}`, { quantity });
};

// 删除购物车商品
const removeCartItem = (itemId) => {
  return del(`/cart/items/${itemId}`);
};

/**
 * 订单相关API
 */

// 创建订单
const createOrder = (orderData) => {
  return post('/orders', orderData);
};

// 获取订单列表
const getOrders = (params = {}) => {
  return get('/orders', params);
};

// 获取订单详情
const getOrderDetail = (orderId) => {
  return get(`/orders/${orderId}`);
};

/**
 * 维修订单相关API - 与抖音小程序保持一致
 */

// 创建维修订单
const createRepairOrder = (orderData) => {
  return post('/repair/create', orderData);
};

// 获取用户维修订单列表
const getRepairOrderList = (openId, status = null) => {
  const url = status ? `/repair/list?openId=${openId}&status=${status}` : `/repair/list?openId=${openId}`;
  return get(url);
};

// 获取维修订单详情
const getRepairOrderDetail = (id, openId) => {
  return get(`/repair/detail/${id}?openId=${openId}`);
};

// 取消维修订单 - 与抖音小程序保持一致
const cancelRepairOrder = (id, openId) => {
  return post('/repair/cancel', { id, openId });
};

// 获取用户维修订单统计
const getRepairOrderStats = (openId) => {
  return get(`/repair/stats?openId=${openId}`);
};

// 兼容旧版本的API
const submitRepairRequest = (repairData) => {
  return post('/repair/create', repairData);
};

const getRepairs = (params = {}) => {
  return get('/repair/list', params);
};

const getRepairDetail = (repairId, openId) => {
  return get(`/repair/detail/${repairId}?openId=${openId}`);
};

const updateRepairStatus = (orderNo, status) => {
  return post('/repair/status', { orderNo, status });
};

/**
 * 工程师相关API - 与抖音小程序保持一致
 */

// 获取所有已审核通过的工程师列表
const getApprovedEngineers = () => {
  return get('/engineers/approved');
};

// 获取在线且可接单的工程师列表
const getAvailableEngineers = () => {
  return get('/engineers/available');
};

// 搜索工程师
const searchEngineers = (keyword) => {
  return get(`/engineers/search?keyword=${keyword}`);
};

// 按专业领域筛选工程师
const getEngineersBySpecialty = (specialty) => {
  return get(`/engineers/specialty?specialty=${specialty}`);
};

// 获取工程师专业领域列表
const getEngineerSpecialties = () => {
  return get('/engineers/specialties');
};

// 兼容旧版本的API
const getEngineers = (params = {}) => {
  return get('/engineers', params);
};

const getEngineerDetail = (engineerId) => {
  return get(`/engineers/${engineerId}`);
};

const submitEngineerApplication = (applicationData) => {
  return post('/engineers/apply', applicationData);
};

/**
 * 服务中心相关API - 与抖音小程序保持一致
 */

// 获取已审核通过的服务网点列表
const getApprovedServiceCenters = () => {
  return get('/service-centers/approved');
};

// 根据位置获取附近的服务网点
const getNearbyServiceCenters = (latitude, longitude, radius = 50) => {
  return get(`/service-centers/nearby?latitude=${latitude}&longitude=${longitude}&radius=${radius}`);
};

// 搜索服务网点
const searchServiceCenters = (keyword) => {
  return get(`/service-centers/search?keyword=${keyword}`);
};

// 获取服务网点详情
const getServiceCenterDetail = (id) => {
  return get(`/service-centers/detail/${id}`);
};

// 兼容旧版本的API
const getServiceCenters = (params = {}) => {
  return get('/service-centers', params);
};

const submitServiceCenterApplication = (applicationData) => {
  return post('/service-centers/apply', applicationData);
};

/**
 * 收藏相关API - 与抖音小程序保持一致
 */

// 添加商品收藏
const addFavorite = (openId, productId) => {
  return post('/favorites/add', { openId, productId });
};

// 取消商品收藏
const removeFavorite = (openId, productId) => {
  return post('/favorites/remove', { openId, productId });
};

// 检查商品是否已收藏
const checkFavorite = (openId, productId) => {
  return get(`/favorites/check?openId=${openId}&productId=${productId}`);
};

// 获取用户收藏的商品列表
const getFavoriteProducts = (openId) => {
  return get(`/favorites/list?openId=${openId}`);
};

// 获取用户收藏数量
const getFavoriteCount = (openId) => {
  return get(`/favorites/count?openId=${openId}`);
};

// 兼容旧版本的API
const addToFavorites = (productId) => {
  return post('/favorites', { productId });
};

const getFavorites = () => {
  return get('/favorites');
};

const removeFromFavorites = (productId) => {
  return del(`/favorites/${productId}`);
};

/**
 * 文件上传API
 */

// 上传图片
const uploadImage = (filePath) => {
  return new Promise((resolve, reject) => {
    wx.uploadFile({
      url: `${API_CONFIG.BASE_URL}/upload/image`,
      filePath: filePath,
      name: 'file',
      success: (res) => {
        try {
          const data = JSON.parse(res.data);
          resolve(data);
        } catch (e) {
          reject({ message: '上传失败' });
        }
      },
      fail: reject
    });
  });
};

/**
 * 图片资源相关API
 */

// 获取轮播图
const getBanners = (pageType) => {
  let url = '/images/banners';
  if (pageType) {
    url += `?pageType=${pageType}`;
  }
  return get(url);
};

// 获取图标
const getIcons = () => {
  return get('/images/icons');
};

// 根据分类获取图片资源
const getImageResources = (category) => {
  return get(`/images/${category}`);
};

// 获取所有图片资源
const getAllImages = () => {
  return get('/images/all');
};

/**
 * 获取服务器图片URL
 */
const getImageUrl = (imagePath) => {
  if (!imagePath) return '';
  if (imagePath.startsWith('http')) return imagePath;
  return `${CONFIG.SERVER_BASE_URL}${imagePath}`;
};

module.exports = {
  // 配置信息
  CONFIG: CONFIG,

  // 基础请求方法
  request,
  get,
  post,
  put,
  del,

  // 认证相关
  code2Session,
  login,
  getUserInfoByOpenId,
  updateUserInfo,
  logout,

  // 地址相关
  getAddressList,
  getDefaultAddress,
  saveAddress,
  deleteAddress,
  setDefaultAddress,

  // 商品相关 - 与抖音小程序保持一致
  getProductCategories,
  getProductList,
  getProductDetail,
  getProductReviews,
  getHotProducts,
  searchProducts,

  // 购物车相关
  addToCart,
  getCart,
  updateCartItem,
  removeCartItem,

  // 订单相关
  createOrder,
  getOrders,
  getOrderDetail,

  // 维修相关 - 与抖音小程序保持一致
  createRepairOrder,
  getRepairOrderList,
  getRepairOrderDetail,
  cancelRepairOrder,
  getRepairOrderStats,
  // 兼容旧版本
  submitRepairRequest,
  getRepairs,
  getRepairDetail,
  updateRepairStatus,

  // 工程师相关 - 与抖音小程序保持一致
  getApprovedEngineers,
  getAvailableEngineers,
  searchEngineers,
  getEngineersBySpecialty,
  getEngineerSpecialties,
  // 兼容旧版本
  getEngineers,
  getEngineerDetail,
  submitEngineerApplication,

  // 服务中心相关 - 与抖音小程序保持一致
  getApprovedServiceCenters,
  getNearbyServiceCenters,
  searchServiceCenters,
  getServiceCenterDetail,
  // 兼容旧版本
  getServiceCenters,
  submitServiceCenterApplication,

  // 收藏相关 - 与抖音小程序保持一致
  addFavorite,
  removeFavorite,
  checkFavorite,
  getFavoriteProducts,
  getFavoriteCount,
  // 兼容旧版本
  addToFavorites,
  getFavorites,
  removeFromFavorites,

  // 文件上传
  uploadImage,

  // 图片资源相关
  getBanners,
  getIcons,
  getImageResources,
  getAllImages,

  // 工具方法
  getImageUrl,

  // 配置
  CONFIG
};
