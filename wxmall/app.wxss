/* 全局样式 */
:root {
  --primary-color: #1e88e5; /* Blue theme color */
  --secondary-color: #64b5f6;
  --accent-color: #0d47a1;
  --text-color: #333333;
  --light-text: #757575;
  --background-color: #f5f5f5;
  --white: #ffffff;
  --border-color: #e0e0e0;
  --success-color: #4caf50;
  --warning-color: #ff9800;
  --error-color: #f44336;
}

page {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  background-color: #f5f5f5;
  color: #333333;
  line-height: 1.5;
  font-size: 14px;
  padding-bottom: 100rpx; /* 为自定义TabBar预留空间 */
}

/* 常用布局类 */
.flex {
  display: flex;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.gap-2 {
  gap: 16rpx;
}

.gap-3 {
  gap: 24rpx;
}

/* 间距类 */
.mt-2 {
  margin-top: 16rpx;
}

.mb-2 {
  margin-bottom: 16rpx;
}

.mt-3 {
  margin-top: 24rpx;
}

.mb-3 {
  margin-bottom: 24rpx;
}

/* 文本类 */
.text-primary {
  color: #1e88e5;
}

.text-success {
  color: #4caf50;
}

.text-warning {
  color: #ff9800;
}

.text-error {
  color: #f44336;
}

.text-light {
  color: #757575;
}

.text-sm {
  font-size: 28rpx;
}

.text-xs {
  font-size: 24rpx;
}

.text-center {
  text-align: center;
}

.font-bold {
  font-weight: 600;
}

/* 卡片组件 */
.card {
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

/* 按钮组件 */
.btn {
  display: inline-block;
  padding: 20rpx 40rpx;
  background-color: #1e88e5;
  color: #ffffff;
  border-radius: 16rpx;
  text-align: center;
  font-weight: 500;
  border: none;
  font-size: 28rpx;
}

.btn-block {
  display: block;
  width: 100%;
}

.btn-outline {
  background-color: transparent;
  border: 1px solid #1e88e5;
  color: #1e88e5;
}

.btn-sm {
  padding: 12rpx 24rpx;
  font-size: 28rpx;
}

/* 分割线 */
.divider {
  height: 1px;
  background-color: #e0e0e0;
  margin: 32rpx 0;
}

/* 头像 */
.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
}

.avatar-lg {
  width: 120rpx;
  height: 120rpx;
}

/* 商品卡片 */
.product-card {
  background-color: #ffffff;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.product-card image {
  width: 100%;
  height: 240rpx;
}

.product-card .info {
  padding: 20rpx;
}

.product-card .title {
  font-size: 28rpx;
  font-weight: 500;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-card .price {
  color: #f44336;
  font-weight: 600;
  font-size: 32rpx;
}

/* 标签 */
.badge {
  display: inline-block;
  padding: 4rpx 16rpx;
  background-color: #1e88e5;
  color: #ffffff;
  border-radius: 8rpx;
  font-size: 24rpx;
  margin-right: 8rpx;
}

.badge-outline {
  background-color: transparent;
  border: 1px solid #1e88e5;
  color: #1e88e5;
}

/* 评分 */
.rating {
  display: flex;
  gap: 4rpx;
  color: #ffc107;
  font-size: 28rpx;
}

/* 搜索框 */
.search-bar {
  display: flex;
  background-color: #f0f0f0;
  border-radius: 16rpx;
  padding: 16rpx 24rpx;
  align-items: center;
  margin-bottom: 32rpx;
}

.search-bar input {
  border: none;
  background-color: transparent;
  flex: 1;
  margin-left: 16rpx;
  font-size: 28rpx;
}

/* 轮播图 */
.banner {
  width: 100%;
  height: 300rpx;
  border-radius: 24rpx;
  overflow: hidden;
  margin-bottom: 32rpx;
}

.banner image {
  width: 100%;
  height: 100%;
}

/* 网格布局 */
.grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
}

.grid-3 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24rpx;
}

/* 数量选择器 */
.quantity-selector {
  display: flex;
  align-items: center;
  border: 1px solid #e0e0e0;
  border-radius: 8rpx;
  overflow: hidden;
  width: fit-content;
}

.quantity-btn {
  width: 56rpx;
  height: 56rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f5f5;
  border: none;
}

.quantity-input {
  width: 80rpx;
  height: 56rpx;
  border: none;
  text-align: center;
  font-size: 28rpx;
}

/* 复选框 */
.checkbox {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  border: 1px solid #e0e0e0;
  display: flex;
  justify-content: center;
  align-items: center;
}

.checkbox.checked {
  background-color: #1e88e5;
  border-color: #1e88e5;
  color: #ffffff;
}

/* 列表项 */
.list-item {
  display: flex;
  padding: 24rpx 0;
  border-bottom: 1px solid #e0e0e0;
}

.list-item:last-child {
  border-bottom: none;
}

.list-item .content {
  flex: 1;
  padding: 0 24rpx;
}

.list-item .title {
  font-weight: 500;
  margin-bottom: 8rpx;
}

.list-item .description {
  font-size: 28rpx;
  color: #757575;
}

/* 标签页 */
.tabs {
  display: flex;
  border-bottom: 1px solid #e0e0e0;
  margin-bottom: 32rpx;
}

.tab {
  padding: 24rpx 32rpx;
  font-size: 28rpx;
  position: relative;
}

.tab.active {
  color: #1e88e5;
  font-weight: 500;
}

.tab.active:after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 4rpx;
  background-color: #1e88e5;
}

/* TabBar 优化样式 - 参考抖音小程序 */
.wx-tabbar {
  height: 80rpx !important;
  border-top: 1px solid #e0e0e0 !important;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05) !important;
  padding-top: 15rpx !important;
  padding-bottom: 15rpx !important;
}

.wx-tabbar-item {
  padding-top: 10rpx !important;
  padding-bottom: 10rpx !important;
  position: relative !important;
}

.wx-tabbar-text {
  font-size: 50rpx !important;
  line-height: 1.2 !important;
  font-weight: 400 !important;
  margin-top: 0 !important;
}

/* 选中状态的文字样式 */
.wx-tabbar-item.wx-tabbar-item_selected .wx-tabbar-text {
  font-weight: 600 !important;
  color: #1e88e5 !important;
  transform: scale(1.05) !important;
}

/* 添加底部指示器 */
.wx-tabbar-item.wx-tabbar-item_selected::after {
  content: '' !important;
  position: absolute !important;
  bottom: 10rpx !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  width: 40rpx !important;
  height: 6rpx !important;
  background-color: #1e88e5 !important;
  border-radius: 3rpx !important;
}
