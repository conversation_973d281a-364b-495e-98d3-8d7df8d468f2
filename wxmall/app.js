const auth = require('./utils/auth');

App({
  globalData: {
    userInfo: null,
    userId: null,
    openId: null,
    sessionKey: null,
    sessionToken: null,
    isLoggedIn: false,
    cartItems: [],
    repairInfo: null,
    primaryColor: '#1e88e5'
  },
  onLaunch: function () {
    // 初始化应用时执行


    // 初始化登录状态
    const loginState = auth.initLoginState();

    // 检查微信小程序的session状态
    wx.checkSession({
      success: () => {
        // 登录态有效


        // 如果本地有登录状态，直接使用
        if (loginState.isLoggedIn) {

        } else {
          // 本地没有登录状态，但微信session有效，尝试静默登录

          this.silentLogin();
        }
      },
      fail: () => {
        // 登录态过期，清除本地状态

        auth.clearLoginState();
      }
    });
  },

  // 静默登录（不需要用户授权）
  silentLogin: function() {
    const api = require('./utils/api');

    wx.login({
      success: (res) => {
        if (res.code) {


          // 发送code到后端，获取用户信息
          api.code2Session(res.code).then(loginRes => {
            if (loginRes.success && loginRes.openId) {



              // 处理用户信息 - 优先使用后端数据库中的真实信息
              let userInfo = {
                nickName: '用户' + Math.floor(Math.random() * 10000), // 默认昵称
                avatarUrl: '',
                gender: '0',
                phone: ''
              };

              // 如果后端返回了用户信息，直接使用（这是数据库中的真实信息）
              if (loginRes.userInfo) {
                userInfo = {
                  nickName: loginRes.userInfo.nickName || userInfo.nickName,
                  avatarUrl: loginRes.userInfo.avatarUrl || '',
                  gender: loginRes.userInfo.gender || '0',
                  phone: loginRes.userInfo.phone || ''
                };

              } else {

              }

              auth.saveLoginState(loginRes.openId, userInfo, loginRes.sessionToken);

            } else {

            }
          }).catch(err => {

          });
        }
      },
      fail: (err) => {

      }
    });
  },





  // 完整登录（需要用户授权）
  // 注意：这个方法必须在用户点击事件中直接调用
  login: function(userInfo = null) {
    const api = require('./utils/api');

    // 如果已经有用户信息，直接进行登录
    if (userInfo) {
      wx.login({
        success: (res) => {
          if (res.code) {


            // 发送 code 和用户信息到后端
            api.login(res.code, userInfo).then(loginRes => {
              if (loginRes.success) {
                // 使用新的认证系统保存登录状态
                const success = auth.saveLoginState(
                  loginRes.openId,
                  loginRes.userInfo || userInfo,
                  loginRes.sessionToken
                );

                if (success) {


                  // 触发登录状态变化事件
                  this.triggerLoginStateChange(true);

                  // 触发用户信息更新事件
                  this.triggerUserInfoUpdate(loginRes.userInfo || userInfo);

                  // 显示成功提示
                  wx.showToast({
                    title: '登录成功',
                    icon: 'success'
                  });
                }
              } else {

                wx.showToast({
                  title: '登录失败',
                  icon: 'none'
                });
              }
            }).catch(err => {

              wx.showToast({
                title: '登录失败，请重试',
                icon: 'none'
              });
            });
          }
        }
      });
      return;
    }

    // 如果没有用户信息，先获取用户信息再登录
    wx.getUserProfile({
      desc: '用于完善会员资料',
      success: (userRes) => {

        // 递归调用，传入用户信息
        this.login(userRes.userInfo);
      },
      fail: (err) => {

        wx.showModal({
          title: '提示',
          content: '需要您的授权才能正常使用小程序功能',
          showCancel: false
        });
      }
    });
  },

  // 获取用户信息
  getUserInfo: function(cb) {
    // 使用新的认证系统获取用户信息
    const loginState = auth.getLoginState();

    if (loginState.isLoggedIn && loginState.userInfo) {
      // 更新全局状态
      this.globalData.userInfo = loginState.userInfo;
      this.globalData.openId = loginState.openId;
      this.globalData.isLoggedIn = true;
      this.globalData.sessionToken = loginState.sessionToken;

      typeof cb === 'function' && cb(loginState.userInfo);
      return loginState.userInfo;
    } else {
      // 没有登录状态，返回null
      typeof cb === 'function' && cb(null);
      return null;
    }
  },

  // 检查登录状态
  checkLoginStatus: function() {
    return auth.isLoggedIn();
  },

  // 退出登录
  logout: function() {
    auth.clearLoginState();

    // 显示提示
    wx.showToast({
      title: '已退出登录',
      icon: 'success'
    });

    // 可以跳转到首页或登录页
    wx.reLaunch({
      url: '/pages/index/index'
    });
  },

  // 添加商品到购物车
  addToCart: function(product, quantity = 1) {
    let cartItems = this.globalData.cartItems;
    let found = false;

    // 检查商品是否已在购物车中
    for (let i = 0; i < cartItems.length; i++) {
      if (cartItems[i].id === product.id) {
        cartItems[i].quantity += quantity;
        found = true;
        break;
      }
    }

    // 如果是新商品，添加到购物车
    if (!found) {
      product.quantity = quantity;
      cartItems.push(product);
    }

    // 更新全局购物车数据
    this.globalData.cartItems = cartItems;
    return cartItems;
  },

  // 从购物车移除商品
  removeFromCart: function(productId) {
    let cartItems = this.globalData.cartItems;
    this.globalData.cartItems = cartItems.filter(item => item.id !== productId);
    return this.globalData.cartItems;
  },

  // 更新购物车商品数量
  updateCartItemQuantity: function(productId, quantity) {
    let cartItems = this.globalData.cartItems;
    for (let i = 0; i < cartItems.length; i++) {
      if (cartItems[i].id === productId) {
        cartItems[i].quantity = quantity;
        break;
      }
    }
    this.globalData.cartItems = cartItems;
    return cartItems;
  },

  // 保存维修信息
  saveRepairInfo: function(repairInfo) {
    this.globalData.repairInfo = repairInfo;
  }
})
