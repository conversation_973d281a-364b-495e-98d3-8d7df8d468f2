package com.zlz.mall_server.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zlz.mall_server.entity.ImageResource;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 图片资源Mapper接口
 */
@Mapper
public interface ImageResourceMapper extends BaseMapper<ImageResource> {

    /**
     * 根据分类查询启用的图片资源
     */
    @Select("SELECT * FROM image_resources WHERE category = #{category} " +
            "AND status = 1 ORDER BY sort_order ASC, created_at ASC")
    List<ImageResource> findByCategory(@Param("category") String category);

    /**
     * 根据分类和页面类型查询启用的轮播图
     */
    @Select("SELECT * FROM image_resources WHERE category = #{category} " +
            "AND (#{pageType} IS NULL OR page_type = #{pageType}) " +
            "AND status = 1 ORDER BY sort_order ASC, created_at ASC")
    List<ImageResource> findBannersByPageType(@Param("category") String category,
                                            @Param("pageType") String pageType);

    /**
     * 获取指定分类下的最大排序号
     */
    @Select("SELECT COALESCE(MAX(sort_order), 0) FROM image_resources " +
            "WHERE category = #{category}")
    Integer getMaxSortOrder(@Param("category") String category);

    /**
     * 获取指定分类和页面类型下的最大排序号
     */
    @Select("SELECT COALESCE(MAX(sort_order), 0) FROM image_resources " +
            "WHERE category = #{category} AND (#{pageType} IS NULL OR page_type = #{pageType})")
    Integer getMaxSortOrderByPageType(@Param("category") String category,
                                    @Param("pageType") String pageType);

    /**
     * 更新排序顺序
     */
    @Select("UPDATE image_resources SET sort_order = #{sortOrder}, updated_at = NOW() " +
            "WHERE id = #{id}")
    int updateSortOrder(@Param("id") Long id, @Param("sortOrder") Integer sortOrder);
}
