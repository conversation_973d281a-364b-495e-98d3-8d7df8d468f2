package com.zlz.mall_server.controller;

import com.zlz.mall_server.model.User;
import com.zlz.mall_server.service.UserService;
import com.zlz.mall_server.utils.WxApiUtil;
import com.zlz.mall_server.utils.UrlUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 微信小程序认证控制器
 * 处理微信小程序的登录、注册等认证相关功能
 */
@RestController
@RequestMapping("/api/auth/wx")
@Slf4j
@CrossOrigin(origins = "*")
public class WxAuthController {

    @Autowired
    private WxApiUtil wxApiUtil;

    @Autowired
    private UserService userService;

    @Autowired
    private UrlUtil urlUtil;

    /**
     * 微信小程序code2session接口
     * 仅获取openId，不需要用户信息
     */
    @PostMapping("/code2session")
    public ResponseEntity<Map<String, Object>> code2Session(@RequestBody Map<String, Object> request) {
        log.info("微信小程序code2session请求: {}", request);

        String code = (String) request.get("code");
        if (code == null || code.trim().isEmpty()) {
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("message", "code参数不能为空");
            return ResponseEntity.badRequest().body(error);
        }

        try {
            // 调用微信API获取openId和sessionKey
            Map<String, Object> wxResult = wxApiUtil.code2Session(code);

            log.info("微信code2Session响应: {}", wxResult);

            if (wxResult.containsKey("error")) {
                // 微信API调用失败
                Object errorMsg = wxResult.get("errmsg");
                Map<String, Object> error = new HashMap<>();
                error.put("success", false);
                error.put("message", "微信登录失败: " + (errorMsg != null ? errorMsg : "未知错误"));
                return ResponseEntity.badRequest().body(error);
            }

            // 检查是否包含openid
            if (!wxResult.containsKey("openid")) {
                Map<String, Object> error = new HashMap<>();
                error.put("success", false);
                error.put("message", "微信登录失败: 响应中没有openid");
                return ResponseEntity.badRequest().body(error);
            }

            String openId = (String) wxResult.get("openid");
            String sessionKey = (String) wxResult.get("session_key");

            // 查询用户是否存在（使用统一的openId字段）
            User user = userService.findByOpenId(openId);

            if (user == null) {
                // 用户不存在，创建新用户（仅保存openId，使用默认信息）
                user = new User();
                user.setOpenId(openId);
                user.setNickname("用户" + System.currentTimeMillis() % 10000); // 默认昵称
                user.setAvatarUrl(""); // 默认头像为空
                user.setCreatedAt(LocalDateTime.now());
                user.setUpdatedAt(LocalDateTime.now());
                user.setStatus(1);
                userService.save(user);
                log.info("创建新微信用户: openId={}, nickname={}", openId, user.getNickname());
            } else {
                // 更新最后登录时间
                user.setLastLoginTime(LocalDateTime.now());
                user.setUpdatedAt(LocalDateTime.now());
                userService.updateById(user);
                log.info("微信用户登录: openId={}, userId={}, nickname={}", openId, user.getId(), user.getNickname());
            }

            // 生成session token
            String sessionToken = UUID.randomUUID().toString();

            // 构建返回的用户信息
            Map<String, Object> returnUserInfo = new HashMap<>();
            returnUserInfo.put("nickName", user.getNickname());
            returnUserInfo.put("avatarUrl", urlUtil.processAvatarUrl(user.getAvatarUrl()));
            returnUserInfo.put("gender", user.getGender());
            returnUserInfo.put("phone", user.getPhone());

            log.info("code2session - 返回给前端的用户信息: nickname={}, avatarUrl={}, gender={}, phone={}",
                user.getNickname(), user.getAvatarUrl(), user.getGender(), user.getPhone());

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("openId", openId);
            result.put("sessionToken", sessionToken);
            result.put("userId", user.getId());
            result.put("userInfo", returnUserInfo);
            result.put("message", "登录成功");

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("微信code2session处理异常: {}", e.getMessage(), e);
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("message", "服务器内部错误");
            return ResponseEntity.status(500).body(error);
        }
    }

    /**
     * 微信小程序完整登录接口
     * 包含用户信息的完整登录
     */
    @PostMapping("/login")
    public ResponseEntity<Map<String, Object>> login(@RequestBody Map<String, Object> request) {
        log.info("微信小程序完整登录请求: {}", request);

        String code = (String) request.get("code");
        Map<String, Object> userInfo = (Map<String, Object>) request.get("userInfo");

        if (code == null || code.trim().isEmpty()) {
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("message", "code参数不能为空");
            return ResponseEntity.badRequest().body(error);
        }

        try {
            // 调用微信API获取openId和sessionKey
            Map<String, Object> wxResult = wxApiUtil.code2Session(code);

            log.info("微信完整登录API响应: {}", wxResult);

            if (wxResult.containsKey("error")) {
                // 微信API调用失败
                Object errorMsg = wxResult.get("errmsg");
                Map<String, Object> error = new HashMap<>();
                error.put("success", false);
                error.put("message", "微信登录失败: " + (errorMsg != null ? errorMsg : "未知错误"));
                return ResponseEntity.badRequest().body(error);
            }

            // 检查是否包含openid
            if (!wxResult.containsKey("openid")) {
                Map<String, Object> error = new HashMap<>();
                error.put("success", false);
                error.put("message", "微信登录失败: 响应中没有openid");
                return ResponseEntity.badRequest().body(error);
            }

            String openId = (String) wxResult.get("openid");
            String sessionKey = (String) wxResult.get("session_key");

            // 查询用户是否存在（使用统一的openId字段）
            User user = userService.findByOpenId(openId);

            if (user == null) {
                // 用户不存在，创建新用户
                user = new User();
                user.setOpenId(openId);
                user.setCreatedAt(LocalDateTime.now());
            }

            // 更新用户信息（只在首次注册时更新，或者用户信息有实际变化时更新）
            boolean isNewUser = (user.getId() == null);

            if (userInfo != null) {
                String nickName = (String) userInfo.get("nickName");
                String avatarUrl = (String) userInfo.get("avatarUrl");
                Object genderObj = userInfo.get("gender");

                log.info("完整登录 - 接收到的用户信息: nickName={}, avatarUrl={}, gender={}, 是否新用户={}, 当前数据库昵称={}",
                    nickName, avatarUrl, genderObj, isNewUser, user.getNickname());

                // 完整登录：对于老用户，不要用微信API的默认信息覆盖数据库中的真实信息

                if (isNewUser) {
                    // 新用户：使用微信API提供的信息，如果是默认值则使用友好的默认值
                    if (nickName != null && !nickName.trim().isEmpty() && !"微信用户".equals(nickName)) {
                        user.setNickname(nickName);
                        log.info("完整登录 - 新用户设置昵称: {}", nickName);
                    } else {
                        user.setNickname("用户" + System.currentTimeMillis() % 10000);
                        log.info("完整登录 - 新用户使用默认昵称: {}", user.getNickname());
                    }

                    if (avatarUrl != null && !avatarUrl.trim().isEmpty()) {
                        user.setAvatarUrl(avatarUrl);
                        log.info("完整登录 - 新用户设置头像: {}", avatarUrl);
                    }

                    if (genderObj != null) {
                        user.setGender(String.valueOf(genderObj));
                        log.info("完整登录 - 新用户设置性别: {}", genderObj);
                    }
                } else {
                    // 老用户：保持数据库中的真实信息，不要被微信API的默认信息覆盖
                    log.info("完整登录 - 老用户保持现有信息: nickname={}, avatarUrl={}",
                        user.getNickname(), user.getAvatarUrl());
                }
            } else {
                log.warn("用户信息为空");
                if (isNewUser && user.getNickname() == null) {
                    user.setNickname("用户" + System.currentTimeMillis() % 10000);
                }
            }

            user.setLastLoginTime(LocalDateTime.now());
            user.setUpdatedAt(LocalDateTime.now());
            user.setStatus(1);

            if (user.getId() == null) {
                userService.save(user);
                log.info("创建新微信用户: openId={}, nickname={}, userId={}", openId, user.getNickname(), user.getId());
            } else {
                userService.updateById(user);
                log.info("更新微信用户信息: openId={}, userId={}, nickname={}", openId, user.getId(), user.getNickname());
            }

            // 重新查询用户信息，确保获取到最新的数据
            User updatedUser = userService.findByOpenId(openId);
            log.info("数据库更新后的用户信息: nickname={}, avatarUrl={}", updatedUser.getNickname(), updatedUser.getAvatarUrl());

            // 生成session token
            String sessionToken = UUID.randomUUID().toString();

            // 构建返回的用户信息（使用更新后的数据）
            Map<String, Object> returnUserInfo = new HashMap<>();
            returnUserInfo.put("nickName", updatedUser.getNickname());
            returnUserInfo.put("avatarUrl", urlUtil.processAvatarUrl(updatedUser.getAvatarUrl()));
            returnUserInfo.put("gender", updatedUser.getGender());

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("openId", openId);
            result.put("sessionToken", sessionToken);
            result.put("userId", user.getId());
            result.put("userInfo", returnUserInfo);
            result.put("message", "登录成功");

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("微信完整登录处理异常: {}", e.getMessage(), e);
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("message", "服务器内部错误");
            return ResponseEntity.status(500).body(error);
        }
    }

    /**
     * 通过openId获取用户信息
     */
    @GetMapping("/getUserInfo")
    public ResponseEntity<Map<String, Object>> getUserInfoByOpenId(@RequestParam String openId) {
        log.info("微信小程序获取用户信息请求: openId={}", openId);

        if (openId == null || openId.trim().isEmpty()) {
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("message", "openId参数不能为空");
            return ResponseEntity.badRequest().body(error);
        }

        try {
            // 查询用户是否存在
            User user = userService.findByOpenId(openId);

            if (user == null) {
                Map<String, Object> error = new HashMap<>();
                error.put("success", false);
                error.put("message", "用户不存在");
                return ResponseEntity.badRequest().body(error);
            }

            // 构建返回的用户信息
            Map<String, Object> returnUserInfo = new HashMap<>();
            returnUserInfo.put("nickName", user.getNickname());
            returnUserInfo.put("avatarUrl", urlUtil.processAvatarUrl(user.getAvatarUrl()));
            returnUserInfo.put("gender", user.getGender());
            returnUserInfo.put("phone", user.getPhone());

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("userInfo", returnUserInfo);
            result.put("message", "获取用户信息成功");

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("获取用户信息异常: {}", e.getMessage(), e);
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("message", "服务器内部错误");
            return ResponseEntity.status(500).body(error);
        }
    }

    /**
     * 更新用户信息
     */
    @PostMapping("/updateUserInfo")
    public ResponseEntity<Map<String, Object>> updateUserInfo(@RequestBody Map<String, Object> request) {
        log.info("微信小程序更新用户信息请求: {}", request);

        String openId = (String) request.get("openId");
        Map<String, Object> userInfo = (Map<String, Object>) request.get("userInfo");

        if (openId == null || openId.trim().isEmpty()) {
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("message", "openId参数不能为空");
            return ResponseEntity.badRequest().body(error);
        }

        try {
            // 查询用户是否存在
            User user = userService.findByOpenId(openId);

            if (user == null) {
                Map<String, Object> error = new HashMap<>();
                error.put("success", false);
                error.put("message", "用户不存在");
                return ResponseEntity.badRequest().body(error);
            }

            // 更新用户信息
            if (userInfo != null) {
                if (userInfo.containsKey("nickName")) {
                    user.setNickname((String) userInfo.get("nickName"));
                }
                if (userInfo.containsKey("avatarUrl")) {
                    user.setAvatarUrl((String) userInfo.get("avatarUrl"));
                }
                if (userInfo.containsKey("gender")) {
                    user.setGender(String.valueOf(userInfo.get("gender")));
                }
                if (userInfo.containsKey("phone")) {
                    user.setPhone((String) userInfo.get("phone"));
                }
                // 注意：province和city字段暂时不存储到数据库
                // 如需要可以后续添加到User模型中
            }

            user.setUpdatedAt(LocalDateTime.now());
            userService.updateById(user);

            log.info("更新用户信息成功: openId={}, nickname={}", openId, user.getNickname());

            // 构建返回的用户信息
            Map<String, Object> returnUserInfo = new HashMap<>();
            returnUserInfo.put("nickName", user.getNickname());
            returnUserInfo.put("avatarUrl", urlUtil.processAvatarUrl(user.getAvatarUrl()));
            returnUserInfo.put("gender", user.getGender());

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("userInfo", returnUserInfo);
            result.put("message", "用户信息更新成功");

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("更新用户信息异常: {}", e.getMessage(), e);
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("message", "服务器内部错误");
            return ResponseEntity.status(500).body(error);
        }
    }

    /**
     * 退出登录
     */
    @PostMapping("/logout")
    public ResponseEntity<Map<String, Object>> logout() {
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("message", "退出登录成功");
        return ResponseEntity.ok(result);
    }
}
