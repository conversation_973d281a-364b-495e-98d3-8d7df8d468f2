package com.zlz.mall_server.controller;

import com.zlz.mall_server.model.RepairOrder;
import com.zlz.mall_server.model.Engineer;
import com.zlz.mall_server.service.RepairOrderService;
import com.zlz.mall_server.service.EngineerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/repair")
@Slf4j
public class RepairOrderController {

    @Autowired
    private RepairOrderService repairOrderService;

    @Autowired
    private EngineerService engineerService;

    /**
     * 创建维修订单
     * @param repairOrder 维修订单信息
     * @return 创建结果
     */
    @PostMapping("/create")
    public ResponseEntity<Map<String, Object>> createRepairOrder(@RequestBody RepairOrder repairOrder) {
        log.info("创建维修订单: {}", repairOrder);
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 验证必要参数
            if (repairOrder.getOpenId() == null || repairOrder.getOpenId().isEmpty()) {
                result.put("success", false);
                result.put("message", "创建维修订单失败: openId不能为空");
                return ResponseEntity.badRequest().body(result);
            }
            
            if (repairOrder.getFaultType() == null || repairOrder.getFaultType().isEmpty()) {
                result.put("success", false);
                result.put("message", "创建维修订单失败: 故障类型不能为空");
                return ResponseEntity.badRequest().body(result);
            }
            
            if (repairOrder.getModel() == null || repairOrder.getModel().isEmpty()) {
                result.put("success", false);
                result.put("message", "创建维修订单失败: 充电桩型号不能为空");
                return ResponseEntity.badRequest().body(result);
            }
            
            if (repairOrder.getDescription() == null || repairOrder.getDescription().isEmpty()) {
                result.put("success", false);
                result.put("message", "创建维修订单失败: 故障描述不能为空");
                return ResponseEntity.badRequest().body(result);
            }
            
            if (repairOrder.getName() == null || repairOrder.getName().isEmpty()) {
                result.put("success", false);
                result.put("message", "创建维修订单失败: 联系人姓名不能为空");
                return ResponseEntity.badRequest().body(result);
            }
            
            if (repairOrder.getPhone() == null || repairOrder.getPhone().isEmpty()) {
                result.put("success", false);
                result.put("message", "创建维修订单失败: 联系电话不能为空");
                return ResponseEntity.badRequest().body(result);
            }
            
            if (repairOrder.getServiceType() == null || repairOrder.getServiceType().isEmpty()) {
                result.put("success", false);
                result.put("message", "创建维修订单失败: 服务方式不能为空");
                return ResponseEntity.badRequest().body(result);
            }
            
            if (repairOrder.getServiceType().equals("home") && 
                (repairOrder.getFullAddress() == null || repairOrder.getFullAddress().isEmpty())) {
                result.put("success", false);
                result.put("message", "创建维修订单失败: 上门维修需要提供地址");
                return ResponseEntity.badRequest().body(result);
            }
            
            // 处理预约日期
            if (repairOrder.getAppointmentDate() == null && repairOrder.getAppointmentTime() != null) {
                String[] timeParts = repairOrder.getAppointmentTime().split(" ");
                if (timeParts.length > 0) {
                    try {
                        LocalDate appointmentDate = LocalDate.parse(timeParts[0], DateTimeFormatter.ISO_DATE);
                        repairOrder.setAppointmentDate(appointmentDate);
                    } catch (Exception e) {
                        result.put("success", false);
                        result.put("message", "创建维修订单失败: 预约日期格式不正确");
                        return ResponseEntity.badRequest().body(result);
                    }
                } else {
                    result.put("success", false);
                    result.put("message", "创建维修订单失败: 预约时间不能为空");
                    return ResponseEntity.badRequest().body(result);
                }
            }
            
            // 设置初始状态为"待接单"
            repairOrder.setStatus("pending");
            
            // 创建维修订单
            RepairOrder createdOrder = repairOrderService.createRepairOrder(repairOrder);
            
            result.put("success", true);
            result.put("message", "创建维修订单成功");
            result.put("orderNo", createdOrder.getOrderNo());
            result.put("order", createdOrder);
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("创建维修订单异常", e);
            result.put("success", false);
            result.put("message", "创建维修订单失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(result);
        }
    }
    
    /**
     * 获取用户维修订单列表
     * @param openId 用户openId
     * @param status 订单状态（可选）
     * @return 维修订单列表
     */
    @GetMapping("/list")
    public ResponseEntity<Map<String, Object>> getRepairOrderList(@RequestParam String openId, 
                                                                 @RequestParam(required = false) String status) {
        log.info("获取用户维修订单列表: openId={}, status={}", openId, status);
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            List<RepairOrder> orders;
            
            if (status != null && !status.isEmpty() && !status.equals("all")) {
                orders = repairOrderService.findByOpenIdAndStatus(openId, status);
            } else {
                orders = repairOrderService.findByOpenId(openId);
            }

            // 为订单添加工程师头像信息
            enrichOrdersWithEngineerAvatar(orders);

            result.put("success", true);
            result.put("orders", orders);
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取用户维修订单列表异常", e);
            result.put("success", false);
            result.put("message", "获取维修订单列表失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(result);
        }
    }
    
    /**
     * 获取维修订单详情（通过订单编号）
     * @param orderNo 订单编号
     * @param openId 用户openId
     * @return 维修订单详情
     */
    @GetMapping("/detail")
    public ResponseEntity<Map<String, Object>> getRepairOrderDetail(@RequestParam String orderNo,
                                                                   @RequestParam String openId) {
        log.info("获取维修订单详情: orderNo={}, openId={}", orderNo, openId);

        Map<String, Object> result = new HashMap<>();

        try {
            RepairOrder order = repairOrderService.findByOrderNo(orderNo);

            if (order == null) {
                result.put("success", false);
                result.put("message", "获取维修订单详情失败: 订单不存在");
                return ResponseEntity.badRequest().body(result);
            }

            if (!order.getOpenId().equals(openId)) {
                result.put("success", false);
                result.put("message", "获取维修订单详情失败: 无权限查看该订单");
                return ResponseEntity.badRequest().body(result);
            }

            // 为订单添加工程师头像信息
            enrichOrderWithEngineerAvatar(order);

            result.put("success", true);
            result.put("order", order);

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取维修订单详情异常", e);
            result.put("success", false);
            result.put("message", "获取维修订单详情失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(result);
        }
    }

    /**
     * 获取维修订单详情（通过订单编号或ID）
     * @param id 订单编号或订单ID
     * @param openId 用户openId
     * @return 维修订单详情
     */
    @GetMapping("/detail/{id}")
    public ResponseEntity<Map<String, Object>> getRepairOrderDetailById(@PathVariable String id,
                                                                        @RequestParam String openId) {
        log.info("获取维修订单详情: id={}, openId={}", id, openId);

        Map<String, Object> result = new HashMap<>();

        try {
            RepairOrder order = null;

            // 判断传入的是订单编号还是数据库ID
            if (id.startsWith("R")) {
                // 以R开头的是订单编号
                order = repairOrderService.findByOrderNo(id);
            } else {
                // 纯数字的是数据库ID
                try {
                    Long orderId = Long.parseLong(id);
                    order = repairOrderService.getById(orderId);
                } catch (NumberFormatException e) {
                    // 如果不是数字，尝试作为订单编号查询
                    order = repairOrderService.findByOrderNo(id);
                }
            }

            if (order == null) {
                result.put("success", false);
                result.put("message", "获取维修订单详情失败: 订单不存在");
                return ResponseEntity.badRequest().body(result);
            }

            if (!order.getOpenId().equals(openId)) {
                result.put("success", false);
                result.put("message", "获取维修订单详情失败: 无权限查看该订单");
                return ResponseEntity.badRequest().body(result);
            }

            // 为订单添加工程师头像信息
            enrichOrderWithEngineerAvatar(order);

            result.put("success", true);
            result.put("order", order);

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取维修订单详情异常", e);
            result.put("success", false);
            result.put("message", "获取维修订单详情失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(result);
        }
    }
    
    /**
     * 取消维修订单
     * @param request 请求体包含订单编号和用户openId
     * @return 取消结果
     */
    @PostMapping("/cancel")
    public ResponseEntity<Map<String, Object>> cancelRepairOrder(@RequestBody Map<String, Object> request) {
        // 支持多种参数名：id, orderNo, orderNumber
        String orderNo = (String) request.get("orderNo");
        if (orderNo == null) {
            orderNo = (String) request.get("id");
        }
        if (orderNo == null) {
            orderNo = (String) request.get("orderNumber");
        }

        String openId = (String) request.get("openId");

        log.info("取消维修订单: orderNo={}, openId={}", orderNo, openId);

        Map<String, Object> result = new HashMap<>();

        // 参数验证
        if (orderNo == null || orderNo.trim().isEmpty()) {
            result.put("success", false);
            result.put("message", "订单编号不能为空");
            return ResponseEntity.badRequest().body(result);
        }

        if (openId == null || openId.trim().isEmpty()) {
            result.put("success", false);
            result.put("message", "用户ID不能为空");
            return ResponseEntity.badRequest().body(result);
        }

        try {
            boolean success = repairOrderService.cancelRepairOrder(orderNo, openId);

            if (success) {
                result.put("success", true);
                result.put("message", "取消维修订单成功");
                return ResponseEntity.ok(result);
            } else {
                result.put("success", false);
                result.put("message", "取消维修订单失败: 订单不存在或已经不能取消");
                return ResponseEntity.badRequest().body(result);
            }
        } catch (Exception e) {
            log.error("取消维修订单异常", e);
            result.put("success", false);
            result.put("message", "取消维修订单失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(result);
        }
    }
    
    /**
     * 更新维修订单状态
     * @param orderNo 订单编号
     * @param status 新状态
     * @return 更新结果
     */
    @PostMapping("/status")
    public ResponseEntity<Map<String, Object>> updateRepairOrderStatus(@RequestParam String orderNo, 
                                                                      @RequestParam String status) {
        log.info("更新维修订单状态: orderNo={}, status={}", orderNo, status);
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            boolean success = repairOrderService.updateRepairOrderStatus(orderNo, status);
            
            if (success) {
                result.put("success", true);
                result.put("message", "更新维修订单状态成功");
                return ResponseEntity.ok(result);
            } else {
                result.put("success", false);
                result.put("message", "更新维修订单状态失败: 订单不存在或状态无效");
                return ResponseEntity.badRequest().body(result);
            }
        } catch (Exception e) {
            log.error("更新维修订单状态异常", e);
            result.put("success", false);
            result.put("message", "更新维修订单状态失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(result);
        }
    }
    
    /**
     * 获取用户维修订单统计
     * @param openId 用户openId
     * @return 各状态订单数量
     */
    @GetMapping("/stats")
    public ResponseEntity<Map<String, Object>> getRepairOrderStats(@RequestParam String openId) {
        log.info("获取用户维修订单统计: openId={}", openId);
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            Map<String, Integer> stats = repairOrderService.getRepairOrderStats(openId);
            
            result.put("success", true);
            result.put("stats", stats);
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取用户维修订单统计异常", e);
            result.put("success", false);
            result.put("message", "获取维修订单统计失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(result);
        }
    }

    /**
     * 为订单列表添加工程师头像信息
     */
    private void enrichOrdersWithEngineerAvatar(List<RepairOrder> orders) {
        for (RepairOrder order : orders) {
            enrichOrderWithEngineerAvatar(order);
        }
    }

    /**
     * 为单个订单添加工程师头像信息
     */
    private void enrichOrderWithEngineerAvatar(RepairOrder order) {
        log.info("=== 工程师头像处理调试 ===");
        log.info("订单ID: {}, 工程师ID: {}", order.getId(), order.getEngineerId());

        if (order.getEngineerId() != null) {
            try {
                Engineer engineer = engineerService.getById(order.getEngineerId());
                log.info("查询到的工程师: {}", engineer != null ? engineer.getName() : "null");

                if (engineer != null) {
                    log.info("工程师头像URL: {}", engineer.getAvatar());
                    if (engineer.getAvatar() != null) {
                        order.setEngineerAvatar(engineer.getAvatar());
                        log.info("已设置工程师头像: {}", engineer.getAvatar());
                    } else {
                        log.warn("工程师头像为空: engineerId={}", order.getEngineerId());
                    }
                } else {
                    log.warn("未找到工程师: engineerId={}", order.getEngineerId());
                }
            } catch (Exception e) {
                log.error("获取工程师头像失败: engineerId={}", order.getEngineerId(), e);
            }
        } else {
            log.info("订单未分配工程师");
        }

        log.info("最终订单工程师头像: {}", order.getEngineerAvatar());
        log.info("========================");
    }
}
