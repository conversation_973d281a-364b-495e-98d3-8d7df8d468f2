package com.zlz.mall_server.controller.admin;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.zlz.mall_server.entity.ImageResource;
import com.zlz.mall_server.service.ImageResourceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.Map;

/**
 * 图片资源管理后台API控制器
 */
@Slf4j
@RestController
@RequestMapping("/admin/api/images")
@CrossOrigin(origins = "*")
public class AdminImageResourceController {

    @Autowired
    private ImageResourceService imageResourceService;

    /**
     * 分页查询图片资源
     */
    @GetMapping("/list")
    public ResponseEntity<Map<String, Object>> getImageList(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String category,
            @RequestParam(required = false) String pageType,
            @RequestParam(required = false) String name) {

        Map<String, Object> result = new HashMap<>();

        try {
            IPage<ImageResource> pageResult = imageResourceService.getImageResourcePage(
                page, size, category, pageType, name);

            result.put("success", true);
            result.put("data", pageResult.getRecords());
            result.put("total", pageResult.getTotal());
            result.put("page", pageResult.getCurrent());
            result.put("size", pageResult.getSize());
            result.put("pages", pageResult.getPages());
            result.put("message", "查询成功");

        } catch (Exception e) {
            log.error("查询图片资源失败", e);
            result.put("success", false);
            result.put("message", "查询失败: " + e.getMessage());
        }

        return ResponseEntity.ok(result);
    }

    /**
     * 上传图片
     */
    @PostMapping("/upload")
    public ResponseEntity<Map<String, Object>> uploadImage(
            @RequestParam("file") MultipartFile file,
            @RequestParam("category") String category,
            @RequestParam(value = "pageType", required = false) String pageType,
            @RequestParam(value = "name", required = false) String name) {

        Map<String, Object> result = new HashMap<>();

        try {
            ImageResource imageResource = imageResourceService.uploadImage(
                file, category, pageType, name, "admin");

            result.put("success", true);
            result.put("data", imageResource);
            result.put("message", "上传成功");

        } catch (Exception e) {
            log.error("上传图片失败", e);
            result.put("success", false);
            result.put("message", "上传失败: " + e.getMessage());
        }

        return ResponseEntity.ok(result);
    }

    /**
     * 批量上传图片
     */
    @PostMapping("/batch-upload")
    public ResponseEntity<Map<String, Object>> batchUploadImages(
            @RequestParam("files") MultipartFile[] files,
            @RequestParam("category") String category,
            @RequestParam(value = "pageType", required = false) String pageType) {

        Map<String, Object> result = new HashMap<>();

        try {
            int successCount = 0;
            int failCount = 0;

            for (MultipartFile file : files) {
                try {
                    imageResourceService.uploadImage(file, category, pageType, null, "admin");
                    successCount++;
                } catch (Exception e) {
                    log.error("批量上传中单个文件失败: {}", file.getOriginalFilename(), e);
                    failCount++;
                }
            }

            result.put("success", true);
            result.put("successCount", successCount);
            result.put("failCount", failCount);
            result.put("message", String.format("批量上传完成，成功: %d, 失败: %d", successCount, failCount));

        } catch (Exception e) {
            log.error("批量上传图片失败", e);
            result.put("success", false);
            result.put("message", "批量上传失败: " + e.getMessage());
        }

        return ResponseEntity.ok(result);
    }

    /**
     * 删除图片
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Map<String, Object>> deleteImage(@PathVariable Long id) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            boolean success = imageResourceService.deleteImage(id);
            
            if (success) {
                result.put("success", true);
                result.put("message", "删除成功");
            } else {
                result.put("success", false);
                result.put("message", "删除失败，图片不存在");
            }
            
        } catch (Exception e) {
            log.error("删除图片失败", e);
            result.put("success", false);
            result.put("message", "删除失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }

    /**
     * 更新图片信息
     */
    @PutMapping("/{id}")
    public ResponseEntity<Map<String, Object>> updateImage(
            @PathVariable Long id,
            @RequestBody Map<String, Object> updateData) {
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            ImageResource imageResource = imageResourceService.getById(id);
            if (imageResource == null) {
                result.put("success", false);
                result.put("message", "图片不存在");
                return ResponseEntity.ok(result);
            }
            
            // 更新字段
            if (updateData.containsKey("name")) {
                imageResource.setName((String) updateData.get("name"));
            }
            if (updateData.containsKey("sortOrder")) {
                imageResource.setSortOrder((Integer) updateData.get("sortOrder"));
            }
            if (updateData.containsKey("status")) {
                imageResource.setStatus((Integer) updateData.get("status"));
            }
            
            boolean success = imageResourceService.updateById(imageResource);
            
            if (success) {
                result.put("success", true);
                result.put("data", imageResource);
                result.put("message", "更新成功");
            } else {
                result.put("success", false);
                result.put("message", "更新失败");
            }
            
        } catch (Exception e) {
            log.error("更新图片失败", e);
            result.put("success", false);
            result.put("message", "更新失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }

    /**
     * 更新图片状态
     */
    @PutMapping("/{id}/status")
    public ResponseEntity<Map<String, Object>> updateImageStatus(
            @PathVariable Long id,
            @RequestParam Integer status) {
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            boolean success = imageResourceService.updateStatus(id, status);
            
            if (success) {
                result.put("success", true);
                result.put("message", "状态更新成功");
            } else {
                result.put("success", false);
                result.put("message", "状态更新失败");
            }
            
        } catch (Exception e) {
            log.error("更新图片状态失败", e);
            result.put("success", false);
            result.put("message", "状态更新失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }

    /**
     * 更新排序顺序
     */
    @PutMapping("/{id}/sort")
    public ResponseEntity<Map<String, Object>> updateSortOrder(
            @PathVariable Long id,
            @RequestParam Integer sortOrder) {
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            boolean success = imageResourceService.updateSortOrder(id, sortOrder);
            
            if (success) {
                result.put("success", true);
                result.put("message", "排序更新成功");
            } else {
                result.put("success", false);
                result.put("message", "排序更新失败");
            }
            
        } catch (Exception e) {
            log.error("更新排序失败", e);
            result.put("success", false);
            result.put("message", "排序更新失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }
}
