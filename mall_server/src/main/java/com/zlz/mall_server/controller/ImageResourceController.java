package com.zlz.mall_server.controller;

import com.zlz.mall_server.entity.ImageResource;
import com.zlz.mall_server.service.ImageResourceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 图片资源公开API控制器（供小程序使用）
 */
@Slf4j
@RestController
@RequestMapping("/api/images")
@CrossOrigin(origins = "*")
public class ImageResourceController {

    @Autowired
    private ImageResourceService imageResourceService;

    /**
     * 获取轮播图列表
     */
    @GetMapping("/banners")
    public ResponseEntity<Map<String, Object>> getBanners(@RequestParam(required = false) String pageType) {
        Map<String, Object> result = new HashMap<>();

        try {
            List<ImageResource> banners = imageResourceService.getBannersByPageType(pageType);

            // 转换为前端需要的格式
            List<Map<String, Object>> bannerList = banners.stream().map(banner -> {
                Map<String, Object> item = new HashMap<>();
                item.put("id", banner.getId());
                item.put("name", banner.getName());
                item.put("url", banner.getFileUrl());
                item.put("pageType", banner.getPageType());
                item.put("sortOrder", banner.getSortOrder());
                return item;
            }).collect(Collectors.toList());

            result.put("success", true);
            result.put("data", bannerList);
            result.put("message", "获取轮播图成功");

            log.info("获取轮播图成功，数量: {}, 页面类型: {}", bannerList.size(), pageType);

        } catch (Exception e) {
            log.error("获取轮播图失败", e);
            result.put("success", false);
            result.put("message", "获取轮播图失败: " + e.getMessage());
        }

        return ResponseEntity.ok(result);
    }

    /**
     * 获取图标列表
     */
    @GetMapping("/icons")
    public ResponseEntity<Map<String, Object>> getIcons() {
        Map<String, Object> result = new HashMap<>();

        try {
            List<ImageResource> icons = imageResourceService.getImagesByCategory(
                ImageResource.Category.ICON);

            // 转换为前端需要的格式
            List<Map<String, Object>> iconList = icons.stream().map(icon -> {
                Map<String, Object> item = new HashMap<>();
                item.put("id", icon.getId());
                item.put("name", icon.getName());
                item.put("url", icon.getFileUrl());
                item.put("sortOrder", icon.getSortOrder());
                return item;
            }).collect(Collectors.toList());

            result.put("success", true);
            result.put("data", iconList);
            result.put("message", "获取图标成功");

            log.info("获取图标成功，数量: {}", iconList.size());

        } catch (Exception e) {
            log.error("获取图标失败", e);
            result.put("success", false);
            result.put("message", "获取图标失败: " + e.getMessage());
        }

        return ResponseEntity.ok(result);
    }

    /**
     * 获取所有图片资源（按分类分组）
     */
    @GetMapping("/all")
    public ResponseEntity<Map<String, Object>> getAllImages() {
        Map<String, Object> result = new HashMap<>();

        try {
            Map<String, Object> data = new HashMap<>();

            // 获取轮播图（按页面类型分组）
            Map<String, Object> banners = new HashMap<>();
            banners.put("home", imageResourceService.getBannersByPageType(ImageResource.PageType.HOME)
                .stream().map(this::convertToMap).collect(Collectors.toList()));
            banners.put("repair", imageResourceService.getBannersByPageType(ImageResource.PageType.REPAIR)
                .stream().map(this::convertToMap).collect(Collectors.toList()));
            data.put("banners", banners);

            // 获取图标
            List<ImageResource> icons = imageResourceService.getImagesByCategory(
                ImageResource.Category.ICON);
            List<Map<String, Object>> iconList = icons.stream()
                .map(this::convertToMap)
                .collect(Collectors.toList());
            data.put("icons", iconList);

            result.put("success", true);
            result.put("data", data);
            result.put("message", "获取图片资源成功");

            log.info("获取所有图片资源成功");

        } catch (Exception e) {
            log.error("获取图片资源失败", e);
            result.put("success", false);
            result.put("message", "获取图片资源失败: " + e.getMessage());
        }

        return ResponseEntity.ok(result);
    }

    /**
     * 根据分类获取图片
     */
    @GetMapping("/{category}")
    public ResponseEntity<Map<String, Object>> getImagesByCategory(@PathVariable String category) {

        Map<String, Object> result = new HashMap<>();

        try {
            List<ImageResource> images = imageResourceService.getImagesByCategory(category);

            List<Map<String, Object>> imageList = images.stream()
                .map(this::convertToMap)
                .collect(Collectors.toList());

            result.put("success", true);
            result.put("data", imageList);
            result.put("message", "获取图片成功");

            log.info("获取{}图片成功，数量: {}", category, imageList.size());

        } catch (Exception e) {
            log.error("获取{}图片失败", category, e);
            result.put("success", false);
            result.put("message", "获取图片失败: " + e.getMessage());
        }

        return ResponseEntity.ok(result);
    }

    /**
     * 转换ImageResource为Map
     */
    private Map<String, Object> convertToMap(ImageResource image) {
        Map<String, Object> item = new HashMap<>();
        item.put("id", image.getId());
        item.put("name", image.getName());
        item.put("url", image.getFileUrl());
        item.put("category", image.getCategory());
        item.put("pageType", image.getPageType());
        item.put("sortOrder", image.getSortOrder());
        item.put("width", image.getWidth());
        item.put("height", image.getHeight());
        return item;
    }
}
