package com.zlz.mall_server.utils;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * 微信小程序API工具类
 * 用于调用微信小程序相关API
 */
@Component
@Slf4j
public class WxApiUtil {

    @Value("${wx.appid}")
    private String appid;

    @Value("${wx.secret}")
    private String secret;

    private final RestTemplate restTemplate = new RestTemplate();
    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 微信小程序登录，code换取openId和sessionKey
     * @param code 微信登录code
     * @return 包含openId和sessionKey的Map
     */
    public Map<String, Object> code2Session(String code) {
        // 微信小程序的登录API
        String url = "https://api.weixin.qq.com/sns/jscode2session" +
                "?appid=" + appid +
                "&secret=" + secret +
                "&js_code=" + code +
                "&grant_type=authorization_code";

        log.info("调用微信code2session API: {}", url.replaceAll("secret=[^&]*", "secret=***"));

        try {
            String responseStr = restTemplate.getForObject(url, String.class);
            log.info("微信code2session响应字符串: {}", responseStr);

            if (responseStr == null || responseStr.trim().isEmpty()) {
                Map<String, Object> errorResult = new HashMap<>();
                errorResult.put("error", true);
                errorResult.put("errmsg", "微信API响应为空");
                return errorResult;
            }

            // 使用Jackson解析JSON响应
            Map<String, Object> response = objectMapper.readValue(responseStr, new TypeReference<Map<String, Object>>() {});
            log.info("解析后的微信响应: {}", response);

            // 检查是否有错误码
            if (response.containsKey("errcode")) {
                Object errcode = response.get("errcode");
                Object errmsg = response.get("errmsg");
                log.error("微信code2session失败: errcode={}, errmsg={}", errcode, errmsg);

                Map<String, Object> errorResult = new HashMap<>();
                errorResult.put("error", true);
                errorResult.put("errcode", errcode);
                errorResult.put("errmsg", errmsg);
                return errorResult;
            }

            return response;
        } catch (Exception e) {
            log.error("调用微信code2session API异常: {}", e.getMessage(), e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("error", true);
            errorResult.put("errmsg", "网络请求失败: " + e.getMessage());
            return errorResult;
        }
    }

    /**
     * 获取微信小程序access_token
     * @return access_token
     */
    public String getAccessToken() {
        String url = "https://api.weixin.qq.com/cgi-bin/token" +
                "?grant_type=client_credential" +
                "&appid=" + appid +
                "&secret=" + secret;

        try {
            String responseStr = restTemplate.getForObject(url, String.class);
            Map<String, Object> response = objectMapper.readValue(responseStr, new TypeReference<Map<String, Object>>() {});

            if (response != null && response.containsKey("access_token")) {
                return (String) response.get("access_token");
            } else {
                log.error("获取微信access_token失败: {}", response);
                return null;
            }
        } catch (Exception e) {
            log.error("获取微信access_token异常: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 验证微信小程序数据签名
     * @param rawData 原始数据
     * @param signature 签名
     * @param sessionKey session_key
     * @return 验证结果
     */
    public boolean verifySignature(String rawData, String signature, String sessionKey) {
        try {
            // 使用Java内置的SHA1算法验证签名
            java.security.MessageDigest md = java.security.MessageDigest.getInstance("SHA-1");
            byte[] hash = md.digest((rawData + sessionKey).getBytes("UTF-8"));

            // 转换为十六进制字符串
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }

            String expectedSignature = hexString.toString();
            return expectedSignature.equals(signature);
        } catch (Exception e) {
            log.error("验证微信签名异常: {}", e.getMessage(), e);
            return false;
        }
    }


}
