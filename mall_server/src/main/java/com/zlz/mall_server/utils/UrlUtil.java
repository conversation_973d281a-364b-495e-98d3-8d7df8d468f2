package com.zlz.mall_server.utils;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * URL处理工具类
 */
@Component
public class UrlUtil {

    @Value("${server.domain:https://localhost:8443}")
    private String serverDomain;

    /**
     * 获取服务器基础URL
     */
    public String getServerBaseUrl() {
        return serverDomain;
    }
    
    /**
     * 处理头像URL，确保返回完整的访问路径
     * @param avatarUrl 原始头像URL
     * @return 完整的头像URL
     */
    public String processAvatarUrl(String avatarUrl) {
        if (avatarUrl == null || avatarUrl.trim().isEmpty()) {
            return null;
        }
        
        // 如果已经是完整URL，直接返回
        if (avatarUrl.startsWith("http://") || avatarUrl.startsWith("https://")) {
            return avatarUrl;
        }
        
        // 如果是服务器上的文件路径，返回完整URL
        if (avatarUrl.startsWith("/uploads/")) {
            return getServerBaseUrl() + avatarUrl;
        }
        
        // 其他情况直接返回
        return avatarUrl;
    }
    
    /**
     * 处理文件URL，确保返回完整的访问路径
     * @param fileUrl 原始文件URL
     * @return 完整的文件URL
     */
    public String processFileUrl(String fileUrl) {
        if (fileUrl == null || fileUrl.trim().isEmpty()) {
            return null;
        }
        
        // 如果已经是完整URL，直接返回
        if (fileUrl.startsWith("http://") || fileUrl.startsWith("https://")) {
            return fileUrl;
        }
        
        // 如果是服务器上的文件路径，返回完整URL
        if (fileUrl.startsWith("/uploads/")) {
            return getServerBaseUrl() + fileUrl;
        }
        
        // 其他情况直接返回
        return fileUrl;
    }
}
