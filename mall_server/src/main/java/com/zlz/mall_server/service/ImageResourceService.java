package com.zlz.mall_server.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zlz.mall_server.entity.ImageResource;
import com.zlz.mall_server.mapper.ImageResourceMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.UUID;

/**
 * 图片资源服务类
 */
@Slf4j
@Service
public class ImageResourceService extends ServiceImpl<ImageResourceMapper, ImageResource> {

    @Value("${file.upload.path:/uploads}")
    private String uploadPath;

    @Value("${server.domain:https://localhost:8443}")
    private String serverDomain;

    /**
     * 分页查询图片资源
     */
    public IPage<ImageResource> getImageResourcePage(int page, int size, String category, String pageType, String name) {
        Page<ImageResource> pageParam = new Page<>(page, size);
        QueryWrapper<ImageResource> queryWrapper = new QueryWrapper<>();

        if (StringUtils.hasText(category)) {
            queryWrapper.eq("category", category);
        }
        if (StringUtils.hasText(pageType)) {
            queryWrapper.eq("page_type", pageType);
        }
        if (StringUtils.hasText(name)) {
            queryWrapper.like("name", name);
        }

        queryWrapper.orderByAsc("category", "page_type", "sort_order", "created_at");

        return this.page(pageParam, queryWrapper);
    }

    /**
     * 根据分类获取启用的图片资源（图标使用）
     */
    public List<ImageResource> getImagesByCategory(String category) {
        return baseMapper.findByCategory(category);
    }

    /**
     * 根据分类和页面类型获取启用的轮播图
     */
    public List<ImageResource> getBannersByPageType(String pageType) {
        return baseMapper.findBannersByPageType(ImageResource.Category.BANNER, pageType);
    }

    /**
     * 上传图片文件
     */
    public ImageResource uploadImage(MultipartFile file, String category, String pageType, String name, String createdBy) throws IOException {
        // 验证文件
        if (file.isEmpty()) {
            throw new IllegalArgumentException("文件不能为空");
        }

        // 验证文件类型
        String contentType = file.getContentType();
        if (!isImageFile(contentType)) {
            throw new IllegalArgumentException("只支持图片文件");
        }

        // 生成文件名
        String originalFilename = file.getOriginalFilename();
        String extension = getFileExtension(originalFilename);
        String filename = generateFilename(category, extension);

        // 创建存储目录
        String categoryPath = "images/" + category + "s/"; // banners, icons
        Path uploadDir = Paths.get(uploadPath, categoryPath);
        Files.createDirectories(uploadDir);

        // 保存文件
        Path filePath = uploadDir.resolve(filename);
        Files.copy(file.getInputStream(), filePath);

        // 获取图片尺寸
        BufferedImage image = ImageIO.read(file.getInputStream());
        int width = image != null ? image.getWidth() : 0;
        int height = image != null ? image.getHeight() : 0;

        // 构建访问URL（使用动态域名）
        String fileUrl = buildFileUrl(categoryPath + filename);

        // 获取下一个排序号
        Integer maxSortOrder;
        if (ImageResource.Category.BANNER.equals(category) && StringUtils.hasText(pageType)) {
            maxSortOrder = baseMapper.getMaxSortOrderByPageType(category, pageType);
        } else {
            maxSortOrder = baseMapper.getMaxSortOrder(category);
        }
        int sortOrder = (maxSortOrder != null ? maxSortOrder : 0) + 1;

        // 创建数据库记录
        ImageResource imageResource = new ImageResource();
        imageResource.setCategory(category);
        imageResource.setPageType(pageType); // 设置页面类型
        imageResource.setName(StringUtils.hasText(name) ? name : originalFilename);
        imageResource.setOriginalFilename(originalFilename);
        imageResource.setFilename(filename);
        imageResource.setFilePath("/" + uploadPath.replaceFirst("^/", "") + "/" + categoryPath);
        imageResource.setFileUrl(fileUrl);
        imageResource.setFileSize(file.getSize());
        imageResource.setMimeType(contentType);
        imageResource.setWidth(width);
        imageResource.setHeight(height);
        imageResource.setSortOrder(sortOrder);
        imageResource.setStatus(ImageResource.Status.ENABLED);
        imageResource.setCreatedBy(createdBy);
        imageResource.setUpdatedBy(createdBy);

        this.save(imageResource);
        
        log.info("图片上传成功: {}", fileUrl);
        return imageResource;
    }

    /**
     * 删除图片资源
     */
    public boolean deleteImage(Long id) {
        ImageResource imageResource = this.getById(id);
        if (imageResource == null) {
            return false;
        }

        // 删除文件
        try {
            Path filePath = Paths.get(imageResource.getFilePath(), imageResource.getFilename());
            Files.deleteIfExists(filePath);
        } catch (IOException e) {
            log.warn("删除文件失败: {}", imageResource.getFileUrl(), e);
        }

        // 删除数据库记录
        return this.removeById(id);
    }

    /**
     * 更新图片状态
     */
    public boolean updateStatus(Long id, Integer status) {
        ImageResource imageResource = new ImageResource();
        imageResource.setId(id);
        imageResource.setStatus(status);
        imageResource.setUpdatedAt(LocalDateTime.now());
        return this.updateById(imageResource);
    }

    /**
     * 更新排序顺序
     */
    public boolean updateSortOrder(Long id, Integer sortOrder) {
        return baseMapper.updateSortOrder(id, sortOrder) > 0;
    }

    /**
     * 验证是否为图片文件
     */
    private boolean isImageFile(String contentType) {
        return contentType != null && contentType.startsWith("image/");
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filename) {
        if (filename == null || !filename.contains(".")) {
            return "";
        }
        return filename.substring(filename.lastIndexOf("."));
    }

    /**
     * 构建文件访问URL（支持动态域名）
     */
    private String buildFileUrl(String relativePath) {
        // 确保路径以/开头
        if (!relativePath.startsWith("/")) {
            relativePath = "/" + relativePath;
        }

        // 确保uploadPath以/开头
        String cleanUploadPath = uploadPath.startsWith("/") ? uploadPath : "/" + uploadPath;

        return serverDomain + cleanUploadPath + relativePath;
    }

    /**
     * 生成文件名
     */
    private String generateFilename(String category, String extension) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String uuid = UUID.randomUUID().toString().substring(0, 8);

        StringBuilder filename = new StringBuilder();
        filename.append(category);
        filename.append("_").append(timestamp).append("_").append(uuid);
        filename.append(extension);

        return filename.toString();
    }
}
