package com.zlz.mall_server.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 图片资源实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("image_resources")
public class ImageResource {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 图片分类：banner(轮播图), icon(图标)
     */
    private String category;

    /**
     * 页面类型：仅轮播图使用，如home(首页), repair(维修页面)等
     */
    private String pageType;

    /**
     * 图片名称/描述
     */
    private String name;

    /**
     * 原始文件名
     */
    private String originalFilename;

    /**
     * 存储文件名
     */
    private String filename;

    /**
     * 文件存储路径
     */
    private String filePath;

    /**
     * 访问URL
     */
    private String fileUrl;

    /**
     * 文件大小(字节)
     */
    private Long fileSize;

    /**
     * 文件MIME类型
     */
    private String mimeType;

    /**
     * 图片宽度
     */
    private Integer width;

    /**
     * 图片高度
     */
    private Integer height;

    /**
     * 显示顺序
     */
    private Integer sortOrder;

    /**
     * 状态：1-启用，0-禁用
     */
    private Integer status;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 更新人
     */
    private String updatedBy;

    // 常量定义
    public static class Category {
        public static final String BANNER = "banner";
        public static final String ICON = "icon";
    }

    public static class PageType {
        public static final String HOME = "home";
        public static final String REPAIR = "repair";
    }

    public static class Status {
        public static final Integer ENABLED = 1;
        public static final Integer DISABLED = 0;
    }
}
