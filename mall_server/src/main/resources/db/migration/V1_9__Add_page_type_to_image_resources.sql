-- 为图片资源表添加页面类型字段
ALTER TABLE image_resources 
ADD COLUMN page_type VARCHAR(50) COMMENT '页面类型：仅轮播图使用，如home(首页), repair(维修页面)等' 
AFTER category;

-- 添加页面类型索引
ALTER TABLE image_resources 
ADD INDEX idx_page_type (page_type),
ADD INDEX idx_category_page_type (category, page_type);

-- 移除旧的sub_category索引（如果存在）
ALTER TABLE image_resources 
DROP INDEX IF EXISTS idx_sub_category;

-- 更新现有轮播图数据，设置页面类型
UPDATE image_resources 
SET page_type = 'home' 
WHERE category = 'banner';

-- 插入维修页面轮播图示例数据
INSERT INTO image_resources (category, page_type, name, original_filename, filename, file_path, file_url, sort_order, status) VALUES
('banner', 'repair', '维修页面轮播图1', 'repair_banner1.jpg', 'banner_repair_001.jpg', '/uploads/images/banners/', '${server.domain}/uploads/images/banners/banner_repair_001.jpg', 1, 1),
('banner', 'repair', '维修页面轮播图2', 'repair_banner2.jpg', 'banner_repair_002.jpg', '/uploads/images/banners/', '${server.domain}/uploads/images/banners/banner_repair_002.jpg', 2, 1),
('banner', 'repair', '维修页面轮播图3', 'repair_banner3.jpg', 'banner_repair_003.jpg', '/uploads/images/banners/', '${server.domain}/uploads/images/banners/banner_repair_003.jpg', 3, 1);

-- 添加更多图标示例数据
INSERT INTO image_resources (category, name, original_filename, filename, file_path, file_url, sort_order, status) VALUES
('icon', '上门维修', 'home_repair.png', 'icon_home_repair.png', '/uploads/images/icons/', '${server.domain}/uploads/images/icons/icon_home_repair.png', 9, 1),
('icon', '网点维修', 'center_repair.png', 'icon_center_repair.png', '/uploads/images/icons/', '${server.domain}/uploads/images/icons/icon_center_repair.png', 10, 1),
('icon', '远程指导', 'remote_guide.png', 'icon_remote_guide.png', '/uploads/images/icons/', '${server.domain}/uploads/images/icons/icon_remote_guide.png', 11, 1),
('icon', '接口损坏', 'port_damage.png', 'icon_port_damage.png', '/uploads/images/icons/', '${server.domain}/uploads/images/icons/icon_port_damage.png', 12, 1),
('icon', '无法启动', 'not_starting.png', 'icon_not_starting.png', '/uploads/images/icons/', '${server.domain}/uploads/images/icons/icon_not_starting.png', 13, 1),
('icon', '过热', 'overheating.png', 'icon_overheating.png', '/uploads/images/icons/', '${server.domain}/uploads/images/icons/icon_overheating.png', 14, 1),
('icon', '显示故障', 'display_issue.png', 'icon_display_issue.png', '/uploads/images/icons/', '${server.domain}/uploads/images/icons/icon_display_issue.png', 15, 1),
('icon', '其他故障', 'other_fault.png', 'icon_other_fault.png', '/uploads/images/icons/', '${server.domain}/uploads/images/icons/icon_other_fault.png', 16, 1);
