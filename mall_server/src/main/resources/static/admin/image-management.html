<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片资源管理 - 桩郎中管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            min-height: 100vh;
            font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .main-container {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(15px);
            border-radius: 16px;
            box-shadow: 0 24px 48px rgba(0, 0, 0, 0.12);
            margin: 24px;
            min-height: calc(100vh - 48px);
        }
        .header-section {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            padding: 3rem 2rem;
            border-radius: 16px 16px 0 0;
            text-align: center;
        }
        .header-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }
        .header-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            margin-bottom: 0;
        }
        .content-section {
            padding: 2rem;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }
        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
        }
        .image-preview {
            max-width: 80px;
            max-height: 80px;
            object-fit: cover;
            border-radius: 12px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        .image-preview:hover {
            transform: scale(1.1);
        }
        .upload-area {
            border: 3px dashed #e0e6ed;
            border-radius: 15px;
            padding: 3rem 2rem;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            background: linear-gradient(45deg, #f8f9fa 0%, #ffffff 100%);
        }
        .upload-area:hover {
            border-color: #2a5298;
            background: linear-gradient(45deg, #f0f6ff 0%, #f8faff 100%);
            transform: translateY(-2px);
        }
        .upload-area.dragover {
            border-color: #2a5298;
            background: linear-gradient(45deg, #e6f2ff 0%, #f0f6ff 100%);
            transform: scale(1.02);
        }
        .upload-icon {
            font-size: 3rem;
            color: #2a5298;
            margin-bottom: 1rem;
        }
        .status-badge {
            font-size: 0.75rem;
            padding: 0.4rem 0.8rem;
            border-radius: 20px;
        }
        .action-buttons .btn {
            margin: 0 3px;
            padding: 0.4rem 0.8rem;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        .action-buttons .btn:hover {
            transform: translateY(-1px);
        }
        .filter-section {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafe 100%);
            border-radius: 12px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 20px rgba(30, 60, 114, 0.08);
            border: 1px solid rgba(42, 82, 152, 0.1);
        }
        .btn-primary {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            border: none;
            border-radius: 8px;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            transition: all 0.3s ease;
            letter-spacing: 0.5px;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(42, 82, 152, 0.4);
            background: linear-gradient(135deg, #1a3464 0%, #245085 100%);
        }
        .table {
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            table-layout: fixed;
            width: 100%;
        }
        .table thead th {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            border: none;
            font-weight: 600;
            padding: 1rem 0.5rem;
            font-size: 0.85rem;
            letter-spacing: 0.3px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .table tbody tr {
            transition: all 0.3s ease;
        }
        .table tbody tr:hover {
            background-color: rgba(42, 82, 152, 0.05);
        }
        .table tbody td {
            padding: 0.75rem 0.5rem;
            vertical-align: middle;
            word-wrap: break-word;
            overflow: hidden;
        }
        .pagination .page-link {
            border-radius: 6px;
            margin: 0 2px;
            border: 1px solid #e9ecef;
            color: #2a5298;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        .pagination .page-item.active .page-link {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            border: none;
            color: white;
        }
        .pagination .page-link:hover {
            background-color: #f0f6ff;
            border-color: #2a5298;
            color: #1e3c72;
        }
        .modal-content {
            border-radius: 20px;
            border: none;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }
        .modal-header {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            border-radius: 16px 16px 0 0;
            border: none;
            padding: 1.5rem 2rem;
        }
        .form-control, .form-select {
            border-radius: 8px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
            padding: 0.75rem 1rem;
        }
        .form-control:focus, .form-select:focus {
            border-color: #2a5298;
            box-shadow: 0 0 0 0.2rem rgba(42, 82, 152, 0.25);
        }
        .btn-light {
            background: rgba(255, 255, 255, 0.9);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: #1e3c72;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-light:hover {
            background: white;
            border-color: white;
            color: #1e3c72;
            transform: translateY(-1px);
        }
    </style>
</head>
<body>
    <div class="container-fluid p-0">
        <div class="main-container">
            <!-- 头部区域 -->
            <div class="header-section">
                <div class="d-flex align-items-center justify-content-center mb-3">
                    <img src="/image/logo/zlz.svg" alt="桩郎中" style="height: 50px; margin-right: 15px;">
                    <div>
                        <h1 class="header-title mb-0">图片资源管理</h1>
                    </div>
                </div>
                <p class="header-subtitle">统一管理小程序图片资源，支持轮播图、图标等多种类型</p>
                <div class="mt-4">
                    <button type="button" class="btn btn-light btn-lg" data-bs-toggle="modal" data-bs-target="#uploadModal">
                        <i class="bi bi-cloud-upload me-2"></i>上传图片
                    </button>
                </div>
            </div>

            <!-- 内容区域 -->
            <div class="content-section">

                <!-- 筛选区域 -->
                <div class="filter-section">
                    <div class="d-flex align-items-center justify-content-between mb-4">
                        <h5 class="mb-0 text-dark">筛选条件</h5>
                        <small class="text-muted">快速查找您需要的图片资源</small>
                    </div>
                    <div class="row g-4">
                        <div class="col-md-2">
                            <label class="form-label fw-bold text-dark">分类</label>
                            <select class="form-select" id="categoryFilter">
                                <option value="">全部分类</option>
                                <option value="banner">轮播图</option>
                                <option value="icon">图标</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label fw-bold text-dark">页面类型</label>
                            <select class="form-select" id="pageTypeFilter">
                                <option value="">全部页面</option>
                                <option value="home">首页</option>
                                <option value="repair">维修页面</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label fw-bold text-dark">图片名称</label>
                            <input type="text" class="form-control" id="nameFilter" placeholder="输入图片名称进行搜索...">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-flex gap-2">
                                <button type="button" class="btn btn-primary flex-fill" onclick="searchImages()">
                                    搜索
                                </button>
                                <button type="button" class="btn btn-outline-secondary" onclick="resetFilters()">
                                    重置
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 图片列表 -->
                <div class="card">
                    <div class="card-header bg-transparent border-0">
                        <div class="d-flex align-items-center justify-content-between">
                            <h5 class="mb-0 text-dark">图片列表</h5>
                            <div class="d-flex align-items-center">
                                <small class="text-muted me-3">
                                    点击图片可预览
                                </small>
                                <span class="badge bg-primary rounded-pill" id="totalCount">0 张图片</span>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th class="text-center" style="width: 80px;">预览</th>
                                        <th>名称</th>
                                        <th class="text-center" style="width: 100px;">分类</th>
                                        <th class="text-center" style="width: 100px;">页面类型</th>
                                        <th class="text-center" style="width: 80px;">尺寸</th>
                                        <th class="text-center" style="width: 80px;">排序</th>
                                        <th class="text-center" style="width: 80px;">状态</th>
                                        <th class="text-center" style="width: 120px;">创建时间</th>
                                        <th class="text-center" style="width: 150px;">操作</th>
                                    </tr>
                                </thead>
                                <tbody id="imageTableBody">
                                    <!-- 动态加载 -->
                                </tbody>
                            </table>
                        </div>

                        <!-- 分页 -->
                        <div class="p-3 bg-light">
                            <nav aria-label="分页导航">
                                <ul class="pagination justify-content-center mb-0" id="pagination">
                                    <!-- 动态生成 -->
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 上传模态框 -->
    <div class="modal fade" id="uploadModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">上传图片资源</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body p-4">
                    <form id="uploadForm">
                        <div class="row g-4">
                            <div class="col-md-4">
                                <label class="form-label fw-bold text-dark">
                                    分类 <span class="text-danger">*</span>
                                </label>
                                <select class="form-select" id="uploadCategory" required>
                                    <option value="">请选择分类</option>
                                    <option value="banner">轮播图</option>
                                    <option value="icon">图标</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label fw-bold text-dark">页面类型</label>
                                <select class="form-select" id="uploadPageType">
                                    <option value="">请选择页面类型</option>
                                    <option value="home">首页</option>
                                    <option value="repair">维修页面</option>
                                </select>
                                <small class="text-muted">仅轮播图需要选择页面类型</small>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label fw-bold text-dark">图片名称</label>
                                <input type="text" class="form-control" id="uploadName" placeholder="可选，不填写将使用文件名">
                            </div>
                            <div class="col-12">
                                <label class="form-label fw-bold text-dark">
                                    选择图片 <span class="text-danger">*</span>
                                </label>
                                <div class="upload-area" id="uploadArea">
                                    <div class="upload-icon">
                                        <i class="bi bi-cloud-upload"></i>
                                    </div>
                                    <h6 class="mb-2">点击选择图片或拖拽图片到此处</h6>
                                    <p class="text-muted mb-0">
                                        支持 JPG、PNG、GIF 格式，单个文件不超过 5MB
                                    </p>
                                    <input type="file" id="fileInput" multiple accept="image/*" style="display: none;">
                                </div>
                                <div id="fileList" class="mt-3"></div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer bg-light">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="uploadImages()">开始上传</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="image-management.js"></script>
</body>
</html>
