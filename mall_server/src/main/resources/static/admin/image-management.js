// 图片管理页面JavaScript

let currentPage = 1;
let pageSize = 10;
let selectedFiles = [];

// 简化的分类配置（移除子分类）
const categoryConfig = {
    banner: '轮播图',
    icon: '图标'
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initPage();
    loadImages();
    initUploadEvents();
});

// 初始化页面
function initPage() {
    // 页面初始化完成
    console.log('图片管理页面初始化完成');
}

// 重置筛选条件
function resetFilters() {
    document.getElementById('categoryFilter').value = '';
    document.getElementById('pageTypeFilter').value = '';
    document.getElementById('nameFilter').value = '';
    loadImages(1);
}

// 初始化上传相关事件
function initUploadEvents() {
    const uploadArea = document.getElementById('uploadArea');
    const fileInput = document.getElementById('fileInput');
    
    // 点击上传区域
    uploadArea.addEventListener('click', () => {
        fileInput.click();
    });
    
    // 文件选择
    fileInput.addEventListener('change', handleFileSelect);
    
    // 拖拽事件
    uploadArea.addEventListener('dragover', (e) => {
        e.preventDefault();
        uploadArea.classList.add('dragover');
    });
    
    uploadArea.addEventListener('dragleave', () => {
        uploadArea.classList.remove('dragover');
    });
    
    uploadArea.addEventListener('drop', (e) => {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
        handleFileSelect({ target: { files: e.dataTransfer.files } });
    });
}

// 处理文件选择
function handleFileSelect(event) {
    const files = Array.from(event.target.files);
    selectedFiles = files;
    displayFileList(files);
}

// 显示文件列表
function displayFileList(files) {
    const fileList = document.getElementById('fileList');
    fileList.innerHTML = '';
    
    files.forEach((file, index) => {
        const fileItem = document.createElement('div');
        fileItem.className = 'border rounded p-2 mb-2 d-flex align-items-center';
        
        // 如果是图片，显示预览
        if (file.type.startsWith('image/')) {
            const img = document.createElement('img');
            img.className = 'me-3';
            img.style.width = '50px';
            img.style.height = '50px';
            img.style.objectFit = 'cover';
            img.style.borderRadius = '4px';
            
            const reader = new FileReader();
            reader.onload = (e) => {
                img.src = e.target.result;
            };
            reader.readAsDataURL(file);
            
            fileItem.appendChild(img);
        }
        
        const fileInfo = document.createElement('div');
        fileInfo.className = 'flex-grow-1';
        fileInfo.innerHTML = `
            <div class="fw-bold">${file.name}</div>
            <div class="text-muted small">${formatFileSize(file.size)}</div>
        `;
        
        const removeBtn = document.createElement('button');
        removeBtn.className = 'btn btn-sm btn-outline-danger';
        removeBtn.innerHTML = '<i class="bi bi-trash"></i>';
        removeBtn.onclick = () => removeFile(index);
        
        fileItem.appendChild(fileInfo);
        fileItem.appendChild(removeBtn);
        fileList.appendChild(fileItem);
    });
}

// 移除文件
function removeFile(index) {
    selectedFiles.splice(index, 1);
    displayFileList(selectedFiles);
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 加载图片列表
function loadImages(page = 1) {
    currentPage = page;

    const category = document.getElementById('categoryFilter').value;
    const pageType = document.getElementById('pageTypeFilter').value;
    const name = document.getElementById('nameFilter').value;

    const params = new URLSearchParams({
        page: page,
        size: pageSize
    });

    if (category) params.append('category', category);
    if (pageType) params.append('pageType', pageType);
    if (name) params.append('name', name);

    fetch(`/admin/api/images/list?${params}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayImages(data.data);
                displayPagination(data.page, data.pages, data.total);
            } else {
                showAlert('加载图片列表失败: ' + data.message, 'danger');
            }
        })
        .catch(error => {
            console.error('加载图片列表失败:', error);
            showAlert('加载图片列表失败', 'danger');
        });
}

// 显示图片列表
function displayImages(images) {
    const tbody = document.getElementById('imageTableBody');
    tbody.innerHTML = '';

    if (images.length === 0) {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td colspan="9" class="text-center py-5">
                <div class="text-muted">
                    <div class="fs-1 mb-3">📷</div>
                    <h5>暂无图片资源</h5>
                    <p>点击上方"上传图片"按钮开始添加图片资源</p>
                </div>
            </td>
        `;
        tbody.appendChild(row);
        return;
    }

    images.forEach(image => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td class="text-center">
                <img src="${image.fileUrl}" class="image-preview" alt="${image.name}"
                     onerror="this.src='/admin/images/no-image.png'"
                     onclick="previewImage('${image.fileUrl}', '${image.name}')"
                     style="cursor: pointer; max-width: 60px; max-height: 60px;">
            </td>
            <td>
                <div class="fw-bold text-truncate" style="max-width: 150px;" title="${image.name}">${image.name}</div>
                <small class="text-muted text-truncate d-block" style="max-width: 150px;" title="${image.originalFilename || ''}">${image.originalFilename || ''}</small>
            </td>
            <td class="text-center">
                <span class="badge bg-primary rounded-pill">${getCategoryText(image.category)}</span>
            </td>
            <td class="text-center">
                ${image.pageType ?
                    `<span class="badge bg-info rounded-pill">${getPageTypeText(image.pageType)}</span>` :
                    '<span class="text-muted">-</span>'
                }
            </td>
            <td class="text-center">
                <small class="text-muted">
                    ${image.width && image.height ? `${image.width}×${image.height}` : '-'}
                </small>
            </td>
            <td class="text-center">
                <input type="number" class="form-control form-control-sm text-center" style="width: 60px;"
                       value="${image.sortOrder}" onchange="updateSortOrder(${image.id}, this.value)"
                       min="0" max="999">
            </td>
            <td class="text-center">
                <span class="badge status-badge rounded-pill ${image.status === 1 ? 'bg-success' : 'bg-secondary'}">
                    ${image.status === 1 ? '启用' : '禁用'}
                </span>
            </td>
            <td class="text-center">
                <small class="text-muted">${formatDateTime(image.createdAt)}</small>
            </td>
            <td class="text-center action-buttons">
                <div class="btn-group-vertical" role="group">
                    <button class="btn btn-sm btn-outline-primary mb-1" onclick="editImage(${image.id})" title="编辑">
                        编辑
                    </button>
                    <button class="btn btn-sm btn-outline-${image.status === 1 ? 'warning' : 'success'} mb-1"
                            onclick="toggleStatus(${image.id}, ${image.status === 1 ? 0 : 1})"
                            title="${image.status === 1 ? '禁用' : '启用'}">
                        ${image.status === 1 ? '禁用' : '启用'}
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="deleteImage(${image.id})" title="删除">
                        删除
                    </button>
                </div>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// 图片预览功能
function previewImage(url, name) {
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-eye me-2"></i>图片预览 - ${name}
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body text-center p-4">
                    <img src="${url}" class="img-fluid rounded" alt="${name}" style="max-height: 70vh;">
                </div>
                <div class="modal-footer bg-light">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <a href="${url}" target="_blank" class="btn btn-primary">
                        <i class="bi bi-download me-1"></i>下载原图
                    </a>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();

    // 模态框关闭后移除DOM元素
    modal.addEventListener('hidden.bs.modal', () => {
        document.body.removeChild(modal);
    });
}

// 显示分页
function displayPagination(current, total, totalCount) {
    const pagination = document.getElementById('pagination');
    const totalCountElement = document.getElementById('totalCount');

    // 更新总数显示
    totalCountElement.textContent = `${totalCount} 张图片`;

    pagination.innerHTML = '';

    if (total <= 1) {
        return; // 只有一页或没有数据时不显示分页
    }

    // 上一页
    const prevLi = document.createElement('li');
    prevLi.className = `page-item ${current <= 1 ? 'disabled' : ''}`;
    prevLi.innerHTML = `
        <a class="page-link" href="#" onclick="loadImages(${current - 1})" ${current <= 1 ? 'tabindex="-1"' : ''}>
            <i class="bi bi-chevron-left"></i> 上一页
        </a>
    `;
    pagination.appendChild(prevLi);

    // 页码
    const startPage = Math.max(1, current - 2);
    const endPage = Math.min(total, current + 2);

    // 如果不是从第1页开始，显示第1页和省略号
    if (startPage > 1) {
        const firstLi = document.createElement('li');
        firstLi.className = 'page-item';
        firstLi.innerHTML = `<a class="page-link" href="#" onclick="loadImages(1)">1</a>`;
        pagination.appendChild(firstLi);

        if (startPage > 2) {
            const ellipsisLi = document.createElement('li');
            ellipsisLi.className = 'page-item disabled';
            ellipsisLi.innerHTML = `<span class="page-link">...</span>`;
            pagination.appendChild(ellipsisLi);
        }
    }

    // 显示页码
    for (let i = startPage; i <= endPage; i++) {
        const li = document.createElement('li');
        li.className = `page-item ${i === current ? 'active' : ''}`;
        li.innerHTML = `<a class="page-link" href="#" onclick="loadImages(${i})">${i}</a>`;
        pagination.appendChild(li);
    }

    // 如果不是到最后一页，显示省略号和最后一页
    if (endPage < total) {
        if (endPage < total - 1) {
            const ellipsisLi = document.createElement('li');
            ellipsisLi.className = 'page-item disabled';
            ellipsisLi.innerHTML = `<span class="page-link">...</span>`;
            pagination.appendChild(ellipsisLi);
        }

        const lastLi = document.createElement('li');
        lastLi.className = 'page-item';
        lastLi.innerHTML = `<a class="page-link" href="#" onclick="loadImages(${total})">${total}</a>`;
        pagination.appendChild(lastLi);
    }

    // 下一页
    const nextLi = document.createElement('li');
    nextLi.className = `page-item ${current >= total ? 'disabled' : ''}`;
    nextLi.innerHTML = `
        <a class="page-link" href="#" onclick="loadImages(${current + 1})" ${current >= total ? 'tabindex="-1"' : ''}>
            下一页 <i class="bi bi-chevron-right"></i>
        </a>
    `;
    pagination.appendChild(nextLi);

    // 页面信息
    const infoLi = document.createElement('li');
    infoLi.className = 'page-item disabled';
    infoLi.innerHTML = `
        <span class="page-link bg-transparent border-0">
            <small class="text-muted">
                第 ${current} 页，共 ${total} 页，${totalCount} 条记录
            </small>
        </span>
    `;
    pagination.appendChild(infoLi);
}

// 搜索图片
function searchImages() {
    loadImages(1);
}

// 上传图片
function uploadImages() {
    if (selectedFiles.length === 0) {
        showAlert('请选择要上传的图片', 'warning');
        return;
    }

    const category = document.getElementById('uploadCategory').value;
    if (!category) {
        showAlert('请选择图片分类', 'warning');
        return;
    }

    const pageType = document.getElementById('uploadPageType').value;
    const name = document.getElementById('uploadName').value;

    // 如果是轮播图但没有选择页面类型，提示用户
    if (category === 'banner' && !pageType) {
        showAlert('轮播图需要选择页面类型', 'warning');
        return;
    }

    // 创建FormData
    const formData = new FormData();

    if (selectedFiles.length === 1) {
        // 单个文件上传
        formData.append('file', selectedFiles[0]);
        formData.append('category', category);
        if (pageType) formData.append('pageType', pageType);
        if (name) formData.append('name', name);

        uploadSingle(formData);
    } else {
        // 批量上传
        selectedFiles.forEach(file => {
            formData.append('files', file);
        });
        formData.append('category', category);
        if (pageType) formData.append('pageType', pageType);

        uploadBatch(formData);
    }
}

// 单个文件上传
function uploadSingle(formData) {
    fetch('/admin/api/images/upload', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('图片上传成功', 'success');
            closeUploadModal();
            loadImages(currentPage);
        } else {
            showAlert('上传失败: ' + data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('上传失败:', error);
        showAlert('上传失败', 'danger');
    });
}

// 批量上传
function uploadBatch(formData) {
    fetch('/admin/api/images/batch-upload', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert(data.message, 'success');
            closeUploadModal();
            loadImages(currentPage);
        } else {
            showAlert('批量上传失败: ' + data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('批量上传失败:', error);
        showAlert('批量上传失败', 'danger');
    });
}

// 关闭上传模态框
function closeUploadModal() {
    const modal = bootstrap.Modal.getInstance(document.getElementById('uploadModal'));
    modal.hide();
    
    // 重置表单
    document.getElementById('uploadForm').reset();
    selectedFiles = [];
    document.getElementById('fileList').innerHTML = '';
}

// 更新排序
function updateSortOrder(id, sortOrder) {
    fetch(`/admin/api/images/${id}/sort?sortOrder=${sortOrder}`, {
        method: 'PUT'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('排序更新成功', 'success');
        } else {
            showAlert('排序更新失败: ' + data.message, 'danger');
            loadImages(currentPage); // 重新加载以恢复原值
        }
    })
    .catch(error => {
        console.error('排序更新失败:', error);
        showAlert('排序更新失败', 'danger');
        loadImages(currentPage);
    });
}

// 切换状态
function toggleStatus(id, status) {
    fetch(`/admin/api/images/${id}/status?status=${status}`, {
        method: 'PUT'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('状态更新成功', 'success');
            loadImages(currentPage);
        } else {
            showAlert('状态更新失败: ' + data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('状态更新失败:', error);
        showAlert('状态更新失败', 'danger');
    });
}

// 删除图片
function deleteImage(id) {
    if (!confirm('确定要删除这张图片吗？删除后无法恢复。')) {
        return;
    }
    
    fetch(`/admin/api/images/${id}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('图片删除成功', 'success');
            loadImages(currentPage);
        } else {
            showAlert('删除失败: ' + data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('删除失败:', error);
        showAlert('删除失败', 'danger');
    });
}

// 编辑图片（简单实现）
function editImage(id) {
    // 这里可以实现更复杂的编辑功能
    const newName = prompt('请输入新的图片名称:');
    if (newName && newName.trim()) {
        fetch(`/admin/api/images/${id}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                name: newName.trim()
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('图片名称更新成功', 'success');
                loadImages(currentPage);
            } else {
                showAlert('更新失败: ' + data.message, 'danger');
            }
        })
        .catch(error => {
            console.error('更新失败:', error);
            showAlert('更新失败', 'danger');
        });
    }
}

// 工具函数
function getCategoryText(category) {
    return categoryConfig[category] || category;
}

function getPageTypeText(pageType) {
    const pageTypeMap = {
        home: '首页',
        repair: '维修页面'
    };
    return pageTypeMap[pageType] || pageType;
}

function formatDateTime(dateTimeStr) {
    if (!dateTimeStr) return '-';
    const date = new Date(dateTimeStr);
    return date.toLocaleString('zh-CN');
}

function showAlert(message, type = 'info') {
    // 简单的提示实现，可以替换为更好的提示组件
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.top = '20px';
    alertDiv.style.right = '20px';
    alertDiv.style.zIndex = '9999';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    // 3秒后自动消失
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.parentNode.removeChild(alertDiv);
        }
    }, 3000);
}
