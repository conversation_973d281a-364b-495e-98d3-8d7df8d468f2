spring:
  application:
    name: mall_server
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: jdbc:mysql://************:3306/mall?serverTimezone=Asia/Shanghai&useSSL=false&characterEncoding=utf8
    username: zlz
    password: zlzZLZ112233!
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 50MB
server:
  port: 8443
  domain: https://www.zhuanglz.cn:8443
  ssl:
    enabled: true
#    key-store: classpath:keystore.p12
#    key-store-password: zlzmall123
#    key-store-type: PKCS12
#    key-alias: zlz-mall
#    key-password: zlzmall123
    key-store: /etc/letsencrypt/live/www.zhuanglz.cn/keystore.p12
    key-store-password: changeit
    key-store-type: PKCS12
    key-alias: tomcat
   # HTTP重定向端口
  http:
    port: 8080

# 抖音小程序配置
tt:
  appid: tt29ef5f52b6e798df01
  secret: 920d8a8d1ed2da34ba8056d094e4ea563cd32473

# 微信小程序配置
wx:
  appid: wxe51751724eb6fa64
  secret: 9b6cc9e1adb522c39b865b080b507f27

# MyBatis-Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
  global-config:
    db-config:
      id-type: auto
      table-underline: true

# 文件上传配置
file:
  upload:
    path: ./uploads/
    # 允许的文件类型
    allowed-types: jpg,jpeg,png,gif,bmp,webp
    # 最大文件大小（字节）
    max-size: 5242880  # 5MB


